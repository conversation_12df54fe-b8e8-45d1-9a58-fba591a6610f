# CMDB元数据中心

基于Golang的CMDB元数据中心，用于从ClickHouse同步集群元数据到Elasticsearch，支持组件版本收集和增量更新。

## 🚀 功能特性

### 核心功能
- **数据同步**: 从ClickHouse同步集群基础数据到Elasticsearch
- **组件版本收集**: 自动收集apiserver、scheduler、controller-manager、core-dns、kube-proxy、eklet等组件信息
- **标准Kubernetes结构体**: 使用`k8s.io/api`标准结构体解析workload，确保类型安全和完整性
- **增量更新**: 智能检测数据变化，只更新有变化的字段
- **按日期分区**: ES中按date字段进行数据分区管理
- **双集群支持**: 支持用户集群和Meta集群的组件信息收集

### 组件分类
| 组件 | 类型 | 工作负载类型 | 数据源 |
|------|------|-------------|--------|
| apiserver | Meta组件 | deployment | Meta集群 |
| scheduler | Meta组件 | deployment | Meta集群 |
| controller-manager | Meta组件 | deployment | Meta集群 |
| eklet | Meta组件 | deployment | Meta集群 |
| core-dns | 用户组件 | deployment | 用户集群 |
| kube-proxy | 用户组件 | daemonset | 用户集群 |

## 🏗️ 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   ClickHouse    │───▶│   数据处理层     │───▶│ Elasticsearch   │
│   (数据源)      │    │   (ETL+组件)     │    │   (目标存储)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                       │                       │
        ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  cluster表      │    │  组件版本收集    │    │  按日期索引     │
│  workload表     │    │  增量检测       │    │  动态标签       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📦 项目结构

```
cmdb-metadata-center/
├── cmd/                    # 应用程序入口
│   └── main.go            # 主程序
├── internal/              # 内部包
│   ├── config/            # 配置管理
│   ├── model/             # 数据模型
│   ├── repository/        # 数据访问层
│   │   ├── clickhouse/    # ClickHouse客户端
│   │   └── elasticsearch/ # Elasticsearch客户端
│   └── service/           # 业务逻辑层
├── pkg/                   # 公共包
│   └── logger/            # 日志管理
├── config/                # 配置文件
├── scripts/               # 脚本文件
├── go.mod                 # Go模块文件
└── README.md              # 项目说明
```

## 🛠️ 快速开始

### 环境要求
- Go 1.19+
- ClickHouse (数据源)
- Elasticsearch 7.x (目标存储)
- Kubernetes API依赖 (k8s.io/api, k8s.io/apimachinery)

### 安装依赖
```bash
go mod tidy
```

### 配置文件
复制并编辑配置文件：
```bash
cp config/config.yaml config/config.local.yaml
# 编辑配置文件，填入正确的数据库连接信息
```

### 本地运行
```bash
# 同步今天的数据
go run cmd/main.go -config config/config.local.yaml

# 同步指定日期的数据
go run cmd/main.go -config config/config.local.yaml -date 2025-06-29

# 试运行模式（不实际写入数据）
go run cmd/main.go -config config/config.local.yaml -date 2025-06-29 -dry-run
```

### 构建部署
```bash
# 构建可执行文件
./scripts/build.sh

# 部署到腾讯云SCF
./scripts/deploy.sh
```

## ⚙️ 配置说明

### ClickHouse配置
```yaml
clickhouse:
  host: "xxx"
  port: 9000
  database: "xxx"
  username: "xxx"
  password: "xxxx"
  max_open_conns: 10
  max_idle_conns: 5
  conn_max_lifetime: "300s"
```

### Elasticsearch配置
```yaml
elasticsearch:
  # 读取配置
  read:
    host: "xxx"
    port: 9200
    username: "xx"
    password: "xxx"
  # 写入配置
  write:
    host: "xxx"
    port: 9200
    username: "xxx"
    password: "xxx"
```

## 🔄 数据流程

### 1. 数据获取
- 从ClickHouse的`cluster`表获取集群基础信息
- 从ClickHouse的`workload`表获取组件工作负载信息

### 2. 组件版本收集
- **用户集群组件**: 直接通过ClusterId查询workload表
- **Meta集群组件**: 先从cluster表解析metaId，再通过namespace和clusterId查询workload表

### 3. 数据处理
- 解析workload的Data字段，提取容器镜像、标签、参数等信息
- 构建完整的ES文档结构
- 检测数据变化，决定是新增还是更新

### 4. 数据写入
- 按日期创建ES索引 (格式: cmdb-cluster-2025-06-29)
- 批量写入或更新文档
- 支持增量更新和存量数据字段修改

## 📊 数据模型

### ES文档结构
```json
{
  "clusterId": "cls-1sfwjl4i",
  "clusterName": "CSIG-TKEx-SZ-C3",
  "clusterType": "tke",
  "region": "szx",
  "version": "1.20.6",
  "deployAddonVersion": {
    "apiserver": {
      "name": "apiserver",
      "image": "ccr.ccs.tencentyun.com/tkeimages/hyperkube:v1.16.3-tke.35",
      "tag": "v1.16.3-tke.35",
      "args": ["--admission-control=...", "--advertise-address=..."],
      "type": "deployment",
      "source": "meta"
    },
    "coredns": {
      "name": "coredns",
      "image": "ccr.ccs.tencentyun.com/library/coredns:1.2.2",
      "tag": "1.2.2",
      "args": ["-conf", "/etc/coredns/Corefile"],
      "type": "deployment",
      "source": "user"
    }
  },
  "date": "2025-06-29",
  "createdAt": "2025-06-30T10:00:00Z",
  "updatedAt": "2025-06-30T10:00:00Z"
}
```

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./internal/service

# 运行测试并显示覆盖率
go test -cover ./...
```

### 测试覆盖的功能
- 集群详细信息解析
- 工作负载组件解析
- 主容器识别逻辑
- 数据更新判断逻辑

## 📈 监控和日志

### 日志配置
```yaml
logging:
  level: "info"      # debug, info, warn, error
  format: "json"     # json, text
  output: "stdout"   # stdout, stderr
```

### 关键指标
- 同步的集群数量
- 新增/更新的集群数量
- 组件版本收集成功率
- 数据处理耗时

## 🚀 部署方式

### 本地部署
适用于开发和测试环境
```bash
go run cmd/main.go -config config/config.yaml
```

### SCF部署
适用于生产环境，支持定时触发
```bash
./scripts/build.sh
./scripts/deploy.sh
```

### Docker部署
```bash
# 构建镜像
docker build -t cmdb-metadata-center .

# 运行容器
docker run -v $(pwd)/config:/app/config cmdb-metadata-center
```

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

如有问题或建议，请联系项目维护者。
