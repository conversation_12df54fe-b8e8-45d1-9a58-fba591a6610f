# CMDB元数据中心 Makefile

# 变量定义
APP_NAME := cmdb-metadata-center
VERSION := $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_TIME := $(shell date +%Y-%m-%d\ %H:%M:%S)
GO_VERSION := $(shell go version | awk '{print $$3}')

# 构建标志
LDFLAGS := -ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GoVersion=$(GO_VERSION)"

# 默认目标
.PHONY: all
all: build

# 构建
.PHONY: build
build:
	@echo "构建 $(APP_NAME)..."
	go build $(LDFLAGS) -o bin/$(APP_NAME) cmd/main.go

# 构建SCF版本
.PHONY: build-scf
build-scf:
	@echo "构建SCF版本..."
	GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o bin/main cmd/main.go
	cd bin && zip -r $(APP_NAME)-scf.zip main

# 运行
.PHONY: run
run:
	@echo "运行 $(APP_NAME)..."
	go run cmd/main.go -config config/config.yaml

# 试运行
.PHONY: dry-run
dry-run:
	@echo "试运行 $(APP_NAME)..."
	go run cmd/main.go -config config/config.yaml -dry-run

# 同步指定日期
.PHONY: sync-date
sync-date:
	@if [ -z "$(DATE)" ]; then echo "请指定日期: make sync-date DATE=2025-06-29"; exit 1; fi
	@echo "同步日期: $(DATE)"
	go run cmd/main.go -config config/config.yaml -date $(DATE)

# 测试
.PHONY: test
test:
	@echo "运行测试..."
	go test -v ./...

# 测试覆盖率
.PHONY: test-coverage
test-coverage:
	@echo "运行测试覆盖率..."
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "覆盖率报告生成: coverage.html"

# 代码检查
.PHONY: lint
lint:
	@echo "运行代码检查..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "golangci-lint 未安装，跳过代码检查"; \
	fi

# 格式化代码
.PHONY: fmt
fmt:
	@echo "格式化代码..."
	go fmt ./...

# 整理依赖
.PHONY: tidy
tidy:
	@echo "整理依赖..."
	go mod tidy

# 清理
.PHONY: clean
clean:
	@echo "清理构建文件..."
	rm -rf bin/
	rm -f coverage.out coverage.html

# 部署到SCF
.PHONY: deploy-scf
deploy-scf: build-scf
	@echo "部署到SCF..."
	./scripts/deploy.sh

# 安装开发工具
.PHONY: install-tools
install-tools:
	@echo "安装开发工具..."
	@if ! command -v golangci-lint >/dev/null 2>&1; then \
		echo "安装 golangci-lint..."; \
		go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest; \
	fi

# 初始化项目
.PHONY: init
init: install-tools tidy
	@echo "初始化项目..."
	mkdir -p bin logs
	chmod +x scripts/*.sh
	@echo "项目初始化完成!"

# 检查配置
.PHONY: check-config
check-config:
	@echo "检查配置文件..."
	@if [ ! -f config/config.yaml ]; then \
		echo "错误: config/config.yaml 不存在"; \
		echo "请复制 config/config.example.yaml 并修改配置"; \
		exit 1; \
	fi
	@echo "配置文件检查通过"

# 验证连接
.PHONY: verify-connections
verify-connections: check-config
	@echo "验证数据库连接..."
	go run cmd/main.go -config config/config.yaml -dry-run

# 完整的CI流程
.PHONY: ci
ci: fmt lint test build
	@echo "CI流程完成"

# 帮助
.PHONY: help
help:
	@echo "可用的目标:"
	@echo "  build              - 构建应用程序"
	@echo "  build-scf          - 构建SCF部署包"
	@echo "  run                - 运行应用程序"
	@echo "  dry-run            - 试运行模式"
	@echo "  sync-date DATE=... - 同步指定日期数据"
	@echo "  test               - 运行测试"
	@echo "  test-coverage      - 运行测试覆盖率"
	@echo "  lint               - 运行代码检查"
	@echo "  fmt                - 格式化代码"
	@echo "  tidy               - 整理依赖"
	@echo "  clean              - 清理构建文件"
	@echo "  deploy-scf         - 部署到SCF"
	@echo "  install-tools      - 安装开发工具"
	@echo "  init               - 初始化项目"
	@echo "  check-config       - 检查配置文件"
	@echo "  verify-connections - 验证数据库连接"
	@echo "  ci                 - 完整的CI流程"
	@echo "  help               - 显示此帮助信息"
