#!/bin/bash

# 简单的ES查询测试

ES_HOST="************"
ES_PORT="9200"
ES_USERNAME="elastic"
ES_PASSWORD="G08bMcIwjL"

TEST_DATE="2025-06-30"
INDEX_NAME="tke-cluster-${TEST_DATE}"

echo "=== 简单ES查询测试 ==="
echo "测试索引: ${INDEX_NAME}"
echo ""

# 1. 测试错误字段名 (clusterId)
echo "1. 测试错误字段名 (clusterId):"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" \
  -H "Content-Type: application/json" \
  "http://${ES_HOST}:${ES_PORT}/${INDEX_NAME}/_search" \
  -d '{
    "query": {
      "terms": {
        "clusterId": ["cls-001juo98", "cls-001yb5ff"]
      }
    },
    "size": 1
  }' | grep -o '"total":[^,}]*'

echo ""

# 2. 测试正确字段名 (clusterID)
echo "2. 测试正确字段名 (clusterID):"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" \
  -H "Content-Type: application/json" \
  "http://${ES_HOST}:${ES_PORT}/${INDEX_NAME}/_search" \
  -d '{
    "query": {
      "terms": {
        "clusterID": ["cls-001juo98", "cls-001yb5ff"]
      }
    },
    "size": 1
  }' | grep -o '"total":[^,}]*'

echo ""

# 3. 查看所有文档数量
echo "3. 索引总文档数量:"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" \
  "http://${ES_HOST}:${ES_PORT}/${INDEX_NAME}/_count" | grep -o '"count":[^,}]*'

echo ""
echo "如果第2个查询有结果，说明字段名修复成功！"
