package main

import (
	"fmt"
	"log"
	"os"

	"git.woa.com/kmetis/cmdb/internal/config"
)

func main() {
	configPath := "config/config.yaml"
	if len(os.Args) > 1 {
		configPath = os.Args[1]
	}

	fmt.Printf("🔍 验证配置文件: %s\n", configPath)
	fmt.Println("================================")

	// 加载配置
	cfg, err := config.Load(configPath)
	if err != nil {
		log.Fatalf("❌ 加载配置失败: %v", err)
	}

	fmt.Println("✅ 配置文件加载成功")
	fmt.Println()

	// 验证并发配置
	fmt.Println("📊 并发配置:")
	fmt.Printf("  ├─ 批次大小: %d\n", cfg.Sync.BatchSize)
	fmt.Printf("  ├─ 并发Worker数: %d\n", cfg.Sync.ConcurrentWorkers)
	fmt.Printf("  ├─ 组件Worker数: %d\n", cfg.Sync.ComponentWorkers)
	fmt.Printf("  ├─ 重试次数: %d\n", cfg.Sync.RetryAttempts)
	fmt.Printf("  └─ 重试延迟: %s\n", cfg.Sync.RetryDelay)
	fmt.Println()

	// 验证ClickHouse配置
	fmt.Println("🗄️  ClickHouse配置:")
	fmt.Printf("  ├─ 主机: %s:%d\n", cfg.ClickHouse.Host, cfg.ClickHouse.Port)
	fmt.Printf("  ├─ 数据库: %s\n", cfg.ClickHouse.Database)
	fmt.Printf("  ├─ 最大连接数: %d\n", cfg.ClickHouse.MaxOpenConns)
	fmt.Printf("  ├─ 空闲连接数: %d\n", cfg.ClickHouse.MaxIdleConns)
	fmt.Printf("  └─ 读取超时: %s\n", cfg.ClickHouse.ReadTimeout)
	fmt.Println()

	// 验证Elasticsearch配置
	fmt.Println("🔍 Elasticsearch配置:")
	fmt.Printf("  ├─ 读取ES: %s:%d\n", cfg.Elasticsearch.ReadConfig.Host, cfg.Elasticsearch.ReadConfig.Port)
	fmt.Printf("  ├─ 写入ES: %s:%d\n", cfg.Elasticsearch.WriteConfig.Host, cfg.Elasticsearch.WriteConfig.Port)
	fmt.Printf("  ├─ 批量大小: %d\n", cfg.Elasticsearch.BulkSize)
	fmt.Printf("  └─ 批量Worker数: %d\n", cfg.Elasticsearch.BulkWorkers)
	fmt.Println()

	// 验证时间偏移配置
	fmt.Println("⏰ 时间偏移配置:")
	fmt.Printf("  ├─ ClickHouse查询偏移: %d天\n", cfg.Sync.CHQueryOffset)
	fmt.Printf("  └─ ES查询偏移: %d天\n", cfg.Sync.ESQueryOffset)
	fmt.Println()

	// 验证组件配置
	fmt.Println("🔧 组件配置:")
	fmt.Printf("  ├─ 组件数量: %d\n", len(cfg.Components))
	fmt.Printf("  ├─ 启用组件收集: %v\n", cfg.GlobalComponents.EnableComponentCollection)
	fmt.Printf("  └─ 组件收集超时: %d秒\n", cfg.GlobalComponents.ComponentCollectionTimeout)
	fmt.Println()

	// 性能建议
	fmt.Println("💡 性能建议:")
	
	// 检查并发配置
	if cfg.Sync.ConcurrentWorkers <= 1 {
		fmt.Println("  ⚠️  建议启用并发模式 (concurrent_workers > 1)")
	} else {
		fmt.Printf("  ✅ 并发模式已启用 (%d个Worker)\n", cfg.Sync.ConcurrentWorkers)
	}

	// 检查连接池配置
	if cfg.ClickHouse.MaxOpenConns < cfg.Sync.ConcurrentWorkers {
		fmt.Printf("  ⚠️  ClickHouse连接池可能不足 (连接数:%d < Worker数:%d)\n", 
			cfg.ClickHouse.MaxOpenConns, cfg.Sync.ConcurrentWorkers)
		fmt.Printf("      建议设置 max_open_conns >= %d\n", cfg.Sync.ConcurrentWorkers*2)
	} else {
		fmt.Println("  ✅ ClickHouse连接池配置合理")
	}

	// 检查批次大小
	if cfg.Sync.BatchSize < 1000 {
		fmt.Println("  ⚠️  批次大小较小，可能影响性能 (建议1000-3000)")
	} else if cfg.Sync.BatchSize > 5000 {
		fmt.Println("  ⚠️  批次大小较大，可能导致内存压力 (建议1000-3000)")
	} else {
		fmt.Println("  ✅ 批次大小配置合理")
	}

	// 估算性能
	fmt.Println()
	fmt.Println("📈 性能估算 (基于40,000集群):")
	
	totalClusters := 40000
	batchSize := cfg.Sync.BatchSize
	workers := cfg.Sync.ConcurrentWorkers
	
	totalBatches := (totalClusters + batchSize - 1) / batchSize
	batchesPerWorker := (totalBatches + workers - 1) / workers
	
	// 估算时间 (假设每个批次平均处理时间)
	avgBatchTimeMinutes := 3.0 // 假设每批次3分钟
	if workers > 1 {
		avgBatchTimeMinutes = 2.0 // 并发模式下每批次2分钟
	}
	
	estimatedTimeMinutes := float64(batchesPerWorker) * avgBatchTimeMinutes
	
	fmt.Printf("  ├─ 总批次数: %d\n", totalBatches)
	fmt.Printf("  ├─ 每个Worker处理批次: %d\n", batchesPerWorker)
	fmt.Printf("  ├─ 预估总耗时: %.0f分钟\n", estimatedTimeMinutes)
	
	if workers > 1 {
		serialTime := float64(totalBatches) * 3.0
		improvement := (serialTime - estimatedTimeMinutes) / serialTime * 100
		fmt.Printf("  └─ 相比串行模式提升: %.1f%%\n", improvement)
	}

	fmt.Println()
	fmt.Println("🎉 配置验证完成！")
}
