#!/bin/bash

# ES索引迁移脚本：从带日期的索引迁移到固定索引

set -e

ES_HOST=${ES_HOST:-"************"}
ES_PORT=${ES_PORT:-"9200"}
ES_USERNAME=${ES_USERNAME:-"elastic"}
ES_PASSWORD=${ES_PASSWORD:-"G08bMcIwjL"}

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}ES索引迁移：从带日期索引迁移到固定索引${NC}"
echo "ES地址: ${ES_HOST}:${ES_PORT}"
echo ""

# 检查ES连接
echo -e "${YELLOW}检查Elasticsearch连接...${NC}"
if ! curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://${ES_HOST}:${ES_PORT}/_cluster/health" > /dev/null; then
    echo -e "${RED}错误: 无法连接到Elasticsearch${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Elasticsearch连接正常${NC}"

# 查看当前索引
echo -e "${YELLOW}查看当前索引...${NC}"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" \
  "http://${ES_HOST}:${ES_PORT}/_cat/indices/*cluster*?v"

echo ""
echo -e "${YELLOW}检查是否存在带日期的索引...${NC}"

# 检查TKE带日期索引
TKE_DATE_INDICES=$(curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" \
  "http://${ES_HOST}:${ES_PORT}/_cat/indices/tke-cluster-*" | wc -l)

# 检查EKS带日期索引  
EKS_DATE_INDICES=$(curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" \
  "http://${ES_HOST}:${ES_PORT}/_cat/indices/eks-cluster-*" | wc -l)

echo "发现TKE带日期索引: $TKE_DATE_INDICES 个"
echo "发现EKS带日期索引: $EKS_DATE_INDICES 个"

if [ "$TKE_DATE_INDICES" -eq 0 ] && [ "$EKS_DATE_INDICES" -eq 0 ]; then
    echo -e "${GREEN}✓ 没有发现带日期的索引，无需迁移${NC}"
else
    echo -e "${YELLOW}⚠️  发现带日期的索引，建议迁移到固定索引${NC}"
    echo ""
    echo "迁移说明："
    echo "1. 旧索引: tke-cluster-2025-06-30, eks-cluster-2025-06-30"
    echo "2. 新索引: tke-cluster, eks-cluster"
    echo "3. 数据通过date字段区分日期"
    echo ""
    
    read -p "是否继续迁移? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "取消迁移"
        exit 0
    fi
fi

# 创建固定的TKE索引
echo -e "${YELLOW}创建固定的TKE集群索引...${NC}"
curl -s -X PUT \
  -u "${ES_USERNAME}:${ES_PASSWORD}" \
  -H "Content-Type: application/json" \
  "http://${ES_HOST}:${ES_PORT}/tke-cluster" \
  -d '{
    "settings": {
      "number_of_shards": 3,
      "number_of_replicas": 1,
      "index.refresh_interval": "30s",
      "index.max_result_window": 50000
    },
    "mappings": {
      "properties": {
        "clusterId": {"type": "keyword"},
        "clusterName": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
        "clusterType": {"type": "keyword"},
        "clusterStatus": {"type": "keyword"},
        "region": {"type": "keyword"},
        "appId": {"type": "keyword"},
        "version": {"type": "keyword"},
        "accountName": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
        "accountType": {"type": "keyword"},
        "accountLevel": {"type": "keyword"},
        "isBigUser": {"type": "integer"},
        "uin": {"type": "keyword"},
        "subAccountUin": {"type": "keyword"},
        "clusterLevel": {"type": "keyword"},
        "nodeCount": {"type": "integer"},
        "cpuCount": {"type": "integer"},
        "memCount": {"type": "integer"},
        "eksPodCount": {"type": "integer"},
        "vpcId": {"type": "keyword"},
        "clusterCIDR": {"type": "keyword"},
        "serviceCIDR": {"type": "keyword"},
        "metaClusterId": {"type": "keyword"},
        "deployAddonVersion": {"type": "object"},
        "tags": {"type": "flattened"},
        "cmdbMd": {"type": "flattened"},
        "date": {"type": "keyword"},
        "createdAt": {"type": "date"},
        "updatedAt": {"type": "date"},
        "rawData": {"type": "object", "enabled": false}
      }
    }
  }' > /dev/null

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ TKE集群索引创建成功${NC}"
else
    echo -e "${YELLOW}⚠️  TKE集群索引可能已存在${NC}"
fi

# 创建固定的EKS索引
echo -e "${YELLOW}创建固定的EKS集群索引...${NC}"
curl -s -X PUT \
  -u "${ES_USERNAME}:${ES_PASSWORD}" \
  -H "Content-Type: application/json" \
  "http://${ES_HOST}:${ES_PORT}/eks-cluster" \
  -d '{
    "settings": {
      "number_of_shards": 3,
      "number_of_replicas": 1,
      "index.refresh_interval": "30s",
      "index.max_result_window": 50000
    },
    "mappings": {
      "properties": {
        "clusterId": {"type": "keyword"},
        "clusterName": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
        "clusterType": {"type": "keyword"},
        "clusterStatus": {"type": "keyword"},
        "region": {"type": "keyword"},
        "appId": {"type": "keyword"},
        "version": {"type": "keyword"},
        "accountName": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
        "accountType": {"type": "keyword"},
        "accountLevel": {"type": "keyword"},
        "isBigUser": {"type": "integer"},
        "uin": {"type": "keyword"},
        "subAccountUin": {"type": "keyword"},
        "clusterLevel": {"type": "keyword"},
        "nodeCount": {"type": "integer"},
        "cpuCount": {"type": "integer"},
        "memCount": {"type": "integer"},
        "eksPodCount": {"type": "integer"},
        "vpcId": {"type": "keyword"},
        "clusterCIDR": {"type": "keyword"},
        "serviceCIDR": {"type": "keyword"},
        "metaClusterId": {"type": "keyword"},
        "deployAddonVersion": {"type": "object"},
        "tags": {"type": "flattened"},
        "cmdbMd": {"type": "flattened"},
        "date": {"type": "keyword"},
        "createdAt": {"type": "date"},
        "updatedAt": {"type": "date"},
        "rawData": {"type": "object", "enabled": false}
      }
    }
  }' > /dev/null

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ EKS集群索引创建成功${NC}"
else
    echo -e "${YELLOW}⚠️  EKS集群索引可能已存在${NC}"
fi

# 验证新索引
echo -e "${YELLOW}验证新索引...${NC}"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" \
  "http://${ES_HOST}:${ES_PORT}/_cat/indices/tke-cluster,eks-cluster?v"

echo ""
echo -e "${GREEN}索引迁移完成！${NC}"
echo ""
echo "现在您有两套索引："
echo "1. 旧索引: tke-cluster-YYYY-MM-DD, eks-cluster-YYYY-MM-DD (可以删除)"
echo "2. 新索引: tke-cluster, eks-cluster (程序将使用这些)"
echo ""
echo "下次运行同步程序时，将使用新的固定索引结构："
echo "  ./bin/cmdb-metadata-center -config config/config.yaml -date 2025-06-30"
echo ""
echo "如果确认新索引工作正常，可以删除旧的带日期的索引："
echo "  curl -X DELETE -u \"elastic:G08bMcIwjL\" \"http://************:9200/tke-cluster-*\""
echo "  curl -X DELETE -u \"elastic:G08bMcIwjL\" \"http://************:9200/eks-cluster-*\""
