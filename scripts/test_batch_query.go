package main

import (
	"fmt"
	"strings"
)

// 模拟测试分批查询逻辑
func main() {
	fmt.Println("🧪 测试分批查询逻辑")
	fmt.Println("===================")

	// 模拟大量集群ID
	totalClusters := 42525
	clusterIds := make([]string, totalClusters)
	for i := 0; i < totalClusters; i++ {
		clusterIds[i] = fmt.Sprintf("cls-%08d", i)
	}

	fmt.Printf("总集群数: %d\n", len(clusterIds))

	// 测试分批逻辑
	const maxBatchSize = 1000
	batchCount := 0
	totalProcessed := 0

	for i := 0; i < len(clusterIds); i += maxBatchSize {
		end := i + maxBatchSize
		if end > len(clusterIds) {
			end = len(clusterIds)
		}

		batchIds := clusterIds[i:end]
		batchCount++
		totalProcessed += len(batchIds)

		// 模拟查询语句长度检查
		queryLength := estimateQueryLength(batchIds)
		
		fmt.Printf("批次 %d: 集群数=%d, 范围=[%d:%d], 查询长度≈%d字符\n", 
			batchCount, len(batchIds), i, end-1, queryLength)

		// 检查是否超过ClickHouse限制（通常是256KB）
		if queryLength > 250000 {
			fmt.Printf("  ⚠️  警告: 查询长度可能超过ClickHouse限制\n")
		} else {
			fmt.Printf("  ✅ 查询长度安全\n")
		}
	}

	fmt.Println()
	fmt.Printf("📊 分批统计:\n")
	fmt.Printf("  ├─ 总批次数: %d\n", batchCount)
	fmt.Printf("  ├─ 处理集群数: %d\n", totalProcessed)
	fmt.Printf("  ├─ 平均每批次: %.1f\n", float64(totalProcessed)/float64(batchCount))
	fmt.Printf("  └─ 预计查询次数: %d (节点) + %d (Pod总数) + %d (eklet Pod) = %d\n", 
		batchCount, batchCount, batchCount, batchCount*3)

	// 性能估算
	fmt.Println()
	fmt.Printf("⏱️  性能估算:\n")
	avgQueryTime := 2.0 // 假设每个查询2秒
	totalQueryTime := float64(batchCount*3) * avgQueryTime
	fmt.Printf("  ├─ 预计查询总时间: %.1f秒 (%.1f分钟)\n", totalQueryTime, totalQueryTime/60)
	
	// 并发优化后的时间
	concurrentWorkers := 8
	concurrentTime := totalQueryTime / float64(concurrentWorkers)
	fmt.Printf("  └─ 并发优化后时间: %.1f秒 (%.1f分钟)\n", concurrentTime, concurrentTime/60)

	fmt.Println()
	fmt.Println("🎉 分批查询逻辑测试完成！")
}

// estimateQueryLength 估算查询语句长度
func estimateQueryLength(clusterIds []string) int {
	// 基础查询语句长度
	baseQuery := `
		SELECT ClusterId, COUNT(*) as node_count
		FROM node
		WHERE DateDay = ?
		AND ClusterId IN (?)
		GROUP BY ClusterId
	`
	
	// IN子句中的集群ID长度
	inClause := "'" + strings.Join(clusterIds, "','") + "'"
	
	return len(baseQuery) + len(inClause)
}
