#!/bin/bash

# CMDB元数据中心部署脚本

set -e

# 配置参数
FUNCTION_NAME=${FUNCTION_NAME:-"cmdb-metadata-center"}
REGION=${REGION:-"ap-guangzhou"}
RUNTIME=${RUNTIME:-"Go1"}
MEMORY_SIZE=${MEMORY_SIZE:-512}
TIMEOUT=${TIMEOUT:-300}
ENVIRONMENT=${ENVIRONMENT:-"production"}

echo "部署SCF函数: ${FUNCTION_NAME}"
echo "地域: ${REGION}"
echo "运行时: ${RUNTIME}"
echo "内存: ${MEMORY_SIZE}MB"
echo "超时: ${TIMEOUT}s"
echo "环境: ${ENVIRONMENT}"

# 检查部署包是否存在
if [ ! -f "bin/cmdb-metadata-center-scf.zip" ]; then
    echo "错误: 部署包不存在，请先运行构建脚本"
    echo "运行: ./scripts/build.sh"
    exit 1
fi

# 检查腾讯云CLI是否安装
if ! command -v tccli &> /dev/null; then
    echo "错误: 腾讯云CLI未安装"
    echo "请安装腾讯云CLI: pip install tccli"
    exit 1
fi

# 检查函数是否已存在
echo "检查函数是否存在..."
FUNCTION_EXISTS=$(tccli scf GetFunction --region ${REGION} --FunctionName ${FUNCTION_NAME} 2>/dev/null || echo "not_found")

if [ "$FUNCTION_EXISTS" = "not_found" ]; then
    echo "创建新函数..."
    tccli scf CreateFunction \
        --region ${REGION} \
        --FunctionName ${FUNCTION_NAME} \
        --Code '{"ZipFile":"'$(base64 -i bin/cmdb-metadata-center-scf.zip | tr -d '\n')'"}' \
        --Handler "main" \
        --Runtime ${RUNTIME} \
        --Description "CMDB元数据中心同步服务" \
        --MemorySize ${MEMORY_SIZE} \
        --Timeout ${TIMEOUT} \
        --Environment '{"Variables":{"ENV":"'${ENVIRONMENT}'"}}' \
        --DeadLetterConfig '{"Type":"","TargetResource":""}' \
        --VpcConfig '{"VpcId":"","SubnetId":""}' \
        --Namespace "default"
    
    echo "函数创建成功!"
else
    echo "更新现有函数..."
    tccli scf UpdateFunctionCode \
        --region ${REGION} \
        --FunctionName ${FUNCTION_NAME} \
        --ZipFile $(base64 -i bin/cmdb-metadata-center-scf.zip | tr -d '\n')
    
    echo "更新函数配置..."
    tccli scf UpdateFunctionConfiguration \
        --region ${REGION} \
        --FunctionName ${FUNCTION_NAME} \
        --Description "CMDB元数据中心同步服务" \
        --MemorySize ${MEMORY_SIZE} \
        --Timeout ${TIMEOUT} \
        --Environment '{"Variables":{"ENV":"'${ENVIRONMENT}'"}}'
    
    echo "函数更新成功!"
fi

# 创建定时触发器（如果不存在）
echo "配置定时触发器..."
TRIGGER_NAME="${FUNCTION_NAME}-timer"

# 检查触发器是否存在
TRIGGER_EXISTS=$(tccli scf ListTriggers --region ${REGION} --FunctionName ${FUNCTION_NAME} --Namespace "default" 2>/dev/null | grep -c "${TRIGGER_NAME}" || echo "0")

if [ "$TRIGGER_EXISTS" = "0" ]; then
    echo "创建定时触发器..."
    tccli scf CreateTrigger \
        --region ${REGION} \
        --FunctionName ${FUNCTION_NAME} \
        --TriggerName ${TRIGGER_NAME} \
        --Type "timer" \
        --TriggerDesc '{"cron":"0 2 * * *"}' \
        --Namespace "default"
    
    echo "定时触发器创建成功! (每天凌晨2点执行)"
else
    echo "定时触发器已存在"
fi

echo "部署完成!"
echo ""
echo "函数信息:"
echo "  函数名称: ${FUNCTION_NAME}"
echo "  地域: ${REGION}"
echo "  触发器: 每天凌晨2点自动执行"
echo ""
echo "手动执行命令:"
echo "  tccli scf Invoke --region ${REGION} --FunctionName ${FUNCTION_NAME}"
