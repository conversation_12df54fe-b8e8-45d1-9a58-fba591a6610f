#!/bin/bash

# CMDB元数据中心 - 快速ES索引初始化脚本（支持TKE和EKS）

set -e

# 配置参数
#ES_HOST=${ES_HOST:-"************"}
#ES_PORT=${ES_PORT:-"9200"}
#ES_USERNAME=${ES_USERNAME:-"elastic"}
#ES_PASSWORD=${ES_PASSWORD:-"G08bMcIwjL"}

ES_HOST=${ES_HOST:-"lb-8c0obspy-9t8hen40zgwxklpv.clb.gz-tencentclb.cloud"}
ES_PORT=${ES_PORT:-"9200"}
ES_USERNAME=${ES_USERNAME:-"elastic"}
ES_PASSWORD=${ES_PASSWORD:-"Q37F3WO6rEreT4Tr"}

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}CMDB元数据中心 - 快速ES索引初始化${NC}"
echo "ES地址: ${ES_HOST}:${ES_PORT}"
echo ""

# 检查ES连接
echo -e "${YELLOW}检查Elasticsearch连接...${NC}"
if ! curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://${ES_HOST}:${ES_PORT}/_cluster/health" > /dev/null; then
    echo -e "${RED}错误: 无法连接到Elasticsearch${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Elasticsearch连接正常${NC}"

# 创建TKE集群索引
echo -e "${YELLOW}创建TKE集群索引...${NC}"
curl -s -X PUT \
  -u "${ES_USERNAME}:${ES_PASSWORD}" \
  -H "Content-Type: application/json" \
  "http://${ES_HOST}:${ES_PORT}/tke-cluster" \
  -d '{
    "settings": {
      "number_of_shards": 3,
      "number_of_replicas": 1,
      "index.refresh_interval": "30s",
      "index.max_result_window": 50000
    },
    "mappings": {
      "properties": {
        "clusterId": {"type": "keyword"},
        "clusterName": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
        "clusterType": {"type": "keyword"},
        "clusterStatus": {"type": "keyword"},
        "region": {"type": "keyword"},
        "appId": {"type": "keyword"},
        "version": {"type": "keyword"},
        "accountName": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
        "accountType": {"type": "keyword"},
        "accountLevel": {"type": "keyword"},
        "isBigUser": {"type": "integer"},
        "uin": {"type": "keyword"},
        "subAccountUin": {"type": "keyword"},
        "clusterLevel": {"type": "keyword"},
        "nodeCount": {"type": "integer"},
        "cpuCount": {"type": "integer"},
        "memCount": {"type": "integer"},
        "eksPodCount": {"type": "integer"},
        "vpcId": {"type": "keyword"},
        "clusterCIDR": {"type": "keyword"},
        "serviceCIDR": {"type": "keyword"},
        "metaClusterId": {"type": "keyword"},
        "deployAddonVersion": {"type": "object"},
        "tags": {"type": "flattened"},
        "cmdbMd": {"type": "flattened"},
        "date": {"type": "keyword"},
        "createdAt": {"type": "date"},
        "updatedAt": {"type": "date"},
        "rawData": {"type": "object", "enabled": false}
      }
    }
  }' > /dev/null

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ TKE集群索引创建成功${NC}"
else
    echo -e "${RED}✗ TKE集群索引创建失败${NC}"
    exit 1
fi

# 创建EKS集群索引
echo -e "${YELLOW}创建EKS集群索引...${NC}"
curl -s -X PUT \
  -u "${ES_USERNAME}:${ES_PASSWORD}" \
  -H "Content-Type: application/json" \
  "http://${ES_HOST}:${ES_PORT}/eks-cluster" \
  -d '{
    "settings": {
      "number_of_shards": 3,
      "number_of_replicas": 1,
      "index.refresh_interval": "30s",
      "index.max_result_window": 50000
    },
    "mappings": {
      "properties": {
        "clusterId": {"type": "keyword"},
        "clusterName": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
        "clusterType": {"type": "keyword"},
        "clusterStatus": {"type": "keyword"},
        "region": {"type": "keyword"},
        "appId": {"type": "keyword"},
        "version": {"type": "keyword"},
        "accountName": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
        "accountType": {"type": "keyword"},
        "accountLevel": {"type": "keyword"},
        "isBigUser": {"type": "integer"},
        "uin": {"type": "keyword"},
        "subAccountUin": {"type": "keyword"},
        "clusterLevel": {"type": "keyword"},
        "nodeCount": {"type": "integer"},
        "cpuCount": {"type": "integer"},
        "memCount": {"type": "integer"},
        "eksPodCount": {"type": "integer"},
        "vpcId": {"type": "keyword"},
        "clusterCIDR": {"type": "keyword"},
        "serviceCIDR": {"type": "keyword"},
        "metaClusterId": {"type": "keyword"},
        "deployAddonVersion": {"type": "object"},
        "tags": {"type": "flattened"},
        "cmdbMd": {"type": "flattened"},
        "date": {"type": "keyword"},
        "createdAt": {"type": "date"},
        "updatedAt": {"type": "date"},
        "rawData": {"type": "object", "enabled": false}
      }
    }
  }' > /dev/null

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ EKS集群索引创建成功${NC}"
else
    echo -e "${RED}✗ EKS集群索引创建失败${NC}"
    exit 1
fi

# 验证索引
echo -e "${YELLOW}验证索引...${NC}"
TKE_INDEX=$(curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://${ES_HOST}:${ES_PORT}/tke-cluster" | jq -r '.tke-cluster // "not_found"')
EKS_INDEX=$(curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://${ES_HOST}:${ES_PORT}/eks-cluster" | jq -r '.eks-cluster // "not_found"')

if [ "$TKE_INDEX" != "not_found" ] && [ "$EKS_INDEX" != "not_found" ]; then
    echo -e "${GREEN}✓ 索引验证成功${NC}"
else
    echo -e "${RED}✗ 索引验证失败${NC}"
    exit 1
fi

# 显示索引列表
echo -e "${YELLOW}当前索引列表:${NC}"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" \
  "http://${ES_HOST}:${ES_PORT}/_cat/indices/tke-cluster,eks-cluster?v"

echo ""
echo -e "${GREEN}ES索引初始化完成！${NC}"
echo ""
echo "已创建的索引:"
echo "  - tke-cluster (TKE集群数据)"
echo "  - eks-cluster (EKS集群数据)"
echo ""
echo "现在可以运行CMDB同步程序了:"
echo "  ./bin/cmdb-metadata-center -config config/config.yaml -date 2025-06-30"
echo ""
echo "注意: 数据通过date字段区分日期，如:"
echo "  - 查询条件: {\"term\": {\"date\": \"2025-06-30\"}}"
echo "  - 索引结构: tke-cluster 和 eks-cluster 固定不变"
