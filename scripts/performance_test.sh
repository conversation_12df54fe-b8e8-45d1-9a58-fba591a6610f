#!/bin/bash

# CMDB并发同步性能测试脚本
# 用于对比串行模式和并发模式的性能差异

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
CONFIG_FILE="config/config.yaml"
TEST_DATE=$(date -d "yesterday" +%Y-%m-%d)
LOG_DIR="logs/performance_test"
RESULTS_FILE="$LOG_DIR/performance_results_$(date +%Y%m%d_%H%M%S).json"

# 创建日志目录
mkdir -p "$LOG_DIR"

echo -e "${BLUE}🚀 CMDB并发同步性能测试${NC}"
echo "=================================="
echo "测试日期: $TEST_DATE"
echo "配置文件: $CONFIG_FILE"
echo "结果文件: $RESULTS_FILE"
echo ""

# 检查配置文件
if [ ! -f "$CONFIG_FILE" ]; then
    echo -e "${RED}❌ 配置文件不存在: $CONFIG_FILE${NC}"
    exit 1
fi

# 备份原始配置
cp "$CONFIG_FILE" "$CONFIG_FILE.backup"
echo -e "${GREEN}✅ 已备份原始配置${NC}"

# 函数：运行同步测试
run_sync_test() {
    local mode=$1
    local workers=$2
    local description=$3
    
    echo -e "${YELLOW}📊 测试: $description${NC}"
    echo "模式: $mode, Worker数: $workers"
    
    # 更新配置
    if [ "$mode" = "concurrent" ]; then
        sed -i.tmp "s/concurrent_workers: [0-9]*/concurrent_workers: $workers/" "$CONFIG_FILE"
    else
        sed -i.tmp "s/concurrent_workers: [0-9]*/concurrent_workers: 1/" "$CONFIG_FILE"
    fi
    
    # 运行测试
    local start_time=$(date +%s)
    local log_file="$LOG_DIR/${mode}_${workers}workers_$(date +%H%M%S).log"
    
    echo "开始时间: $(date)"
    
    if timeout 3600 go run cmd/main.go -config="$CONFIG_FILE" -date="$TEST_DATE" > "$log_file" 2>&1; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        echo -e "${GREEN}✅ 测试完成${NC}"
        echo "耗时: ${duration}秒 ($(($duration / 60))分钟)"
        
        # 提取性能指标
        extract_metrics "$log_file" "$mode" "$workers" "$duration"
    else
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        echo -e "${RED}❌ 测试失败或超时${NC}"
        echo "耗时: ${duration}秒"
        
        # 记录失败结果
        echo "{\"mode\": \"$mode\", \"workers\": $workers, \"status\": \"failed\", \"duration\": $duration}" >> "$RESULTS_FILE"
    fi
    
    echo ""
}

# 函数：提取性能指标
extract_metrics() {
    local log_file=$1
    local mode=$2
    local workers=$3
    local duration=$4
    
    # 从日志中提取指标
    local total_clusters=$(grep -o "总集群数=[0-9]*" "$log_file" | tail -1 | grep -o "[0-9]*" || echo "0")
    local processed_clusters=$(grep -o "处理=[0-9]*" "$log_file" | tail -1 | grep -o "[0-9]*" || echo "0")
    local new_clusters=$(grep -o "新增=[0-9]*" "$log_file" | tail -1 | grep -o "[0-9]*" || echo "0")
    local updated_clusters=$(grep -o "更新=[0-9]*" "$log_file" | tail -1 | grep -o "[0-9]*" || echo "0")
    local clusters_per_second=$(echo "scale=2; $processed_clusters / $duration" | bc -l 2>/dev/null || echo "0")
    
    # 提取预处理时间
    local preprocess_time=$(grep -o "预处理.*耗时: [0-9.]*[a-z]*" "$log_file" | grep -o "[0-9.]*[a-z]*" | tail -1 || echo "0s")
    
    # 提取错误数量
    local error_count=$(grep -c "ERROR\|❌" "$log_file" || echo "0")
    
    # 生成JSON结果
    cat >> "$RESULTS_FILE" << EOF
{
  "mode": "$mode",
  "workers": $workers,
  "status": "success",
  "duration": $duration,
  "total_clusters": $total_clusters,
  "processed_clusters": $processed_clusters,
  "new_clusters": $new_clusters,
  "updated_clusters": $updated_clusters,
  "clusters_per_second": $clusters_per_second,
  "preprocess_time": "$preprocess_time",
  "error_count": $error_count,
  "timestamp": "$(date -Iseconds)"
},
EOF
    
    echo "指标已记录到: $RESULTS_FILE"
}

# 函数：生成性能报告
generate_report() {
    echo -e "${BLUE}📈 生成性能报告${NC}"
    
    # 移除最后一个逗号
    sed -i '$ s/,$//' "$RESULTS_FILE"
    
    # 包装为JSON数组
    sed -i '1i[' "$RESULTS_FILE"
    echo ']' >> "$RESULTS_FILE"
    
    # 生成可读报告
    local report_file="$LOG_DIR/performance_report_$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$report_file" << EOF
CMDB并发同步性能测试报告
========================

测试时间: $(date)
测试日期: $TEST_DATE

性能对比结果:
EOF
    
    # 解析JSON结果并生成报告
    if command -v jq >/dev/null 2>&1; then
        echo "" >> "$report_file"
        jq -r '.[] | select(.status == "success") | "模式: \(.mode), Worker数: \(.workers), 耗时: \(.duration)秒, 处理速度: \(.clusters_per_second)集群/秒, 总集群: \(.total_clusters), 处理: \(.processed_clusters)"' "$RESULTS_FILE" >> "$report_file"
        
        echo "" >> "$report_file"
        echo "性能提升分析:" >> "$report_file"
        
        # 计算性能提升
        local serial_duration=$(jq -r '.[] | select(.mode == "serial" and .status == "success") | .duration' "$RESULTS_FILE" | head -1)
        local best_concurrent_duration=$(jq -r '.[] | select(.mode == "concurrent" and .status == "success") | .duration' "$RESULTS_FILE" | sort -n | head -1)
        
        if [ "$serial_duration" != "null" ] && [ "$best_concurrent_duration" != "null" ] && [ "$serial_duration" != "" ] && [ "$best_concurrent_duration" != "" ]; then
            local improvement=$(echo "scale=2; ($serial_duration - $best_concurrent_duration) / $serial_duration * 100" | bc -l)
            local speedup=$(echo "scale=2; $serial_duration / $best_concurrent_duration" | bc -l)
            
            echo "串行模式耗时: ${serial_duration}秒" >> "$report_file"
            echo "最佳并发耗时: ${best_concurrent_duration}秒" >> "$report_file"
            echo "性能提升: ${improvement}%" >> "$report_file"
            echo "加速比: ${speedup}x" >> "$report_file"
        fi
    else
        echo "注意: 安装jq以获得更详细的报告分析" >> "$report_file"
    fi
    
    echo -e "${GREEN}✅ 报告已生成: $report_file${NC}"
    cat "$report_file"
}

# 主测试流程
main() {
    echo -e "${YELLOW}开始性能测试...${NC}"
    
    # 初始化结果文件
    echo "" > "$RESULTS_FILE"
    
    # 测试1: 串行模式（基准测试）
    run_sync_test "serial" 1 "串行模式基准测试"
    
    # 测试2: 并发模式 - 4个Worker
    run_sync_test "concurrent" 4 "并发模式 - 4个Worker"
    
    # 测试3: 并发模式 - 8个Worker
    run_sync_test "concurrent" 8 "并发模式 - 8个Worker"
    
    # 测试4: 并发模式 - 12个Worker
    run_sync_test "concurrent" 12 "并发模式 - 12个Worker"
    
    # 测试5: 并发模式 - 16个Worker（压力测试）
    run_sync_test "concurrent" 16 "并发模式 - 16个Worker（压力测试）"
    
    # 生成报告
    generate_report
    
    # 恢复原始配置
    mv "$CONFIG_FILE.backup" "$CONFIG_FILE"
    echo -e "${GREEN}✅ 已恢复原始配置${NC}"
    
    echo -e "${BLUE}🎉 性能测试完成！${NC}"
    echo "结果文件: $RESULTS_FILE"
    echo "日志目录: $LOG_DIR"
}

# 清理函数
cleanup() {
    echo -e "${YELLOW}⚠️  测试被中断，正在清理...${NC}"
    
    # 恢复配置文件
    if [ -f "$CONFIG_FILE.backup" ]; then
        mv "$CONFIG_FILE.backup" "$CONFIG_FILE"
        echo -e "${GREEN}✅ 已恢复原始配置${NC}"
    fi
    
    exit 1
}

# 设置信号处理
trap cleanup INT TERM

# 检查依赖
if ! command -v go >/dev/null 2>&1; then
    echo -e "${RED}❌ Go未安装或不在PATH中${NC}"
    exit 1
fi

if ! command -v bc >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  bc未安装，某些计算可能不准确${NC}"
fi

# 运行主程序
main

echo -e "${GREEN}✨ 所有测试完成！${NC}"
