#!/bin/bash

# CMDB元数据中心 - Elasticsearch索引初始化脚本

set -e

# 配置参数
ES_HOST=${ES_HOST:-"************"}
ES_PORT=${ES_PORT:-"9200"}
ES_USERNAME=${ES_USERNAME:-"elastic"}
ES_PASSWORD=${ES_PASSWORD:-"G08bMcIwjL"}
INDEX_PREFIX=${INDEX_PREFIX:-"cmdb"}

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}CMDB元数据中心 - Elasticsearch索引初始化${NC}"
echo "ES地址: ${ES_HOST}:${ES_PORT}"
echo "索引前缀: ${INDEX_PREFIX}"
echo ""

# 检查ES连接
echo -e "${YELLOW}检查Elasticsearch连接...${NC}"
if ! curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://${ES_HOST}:${ES_PORT}/_cluster/health" > /dev/null; then
    echo -e "${RED}错误: 无法连接到Elasticsearch${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Elasticsearch连接正常${NC}"

# 创建索引模板
echo -e "${YELLOW}创建索引模板...${NC}"

# 索引模板JSON
INDEX_TEMPLATE='{
  "index_patterns": ["'${INDEX_PREFIX}'-cluster-*"],
  "template": {
    "settings": {
      "number_of_shards": 3,
      "number_of_replicas": 1,
      "index.refresh_interval": "30s",
      "index.max_result_window": 50000
    },
    "mappings": {
      "properties": {
        "clusterId": {
          "type": "keyword"
        },
        "clusterName": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword"
            }
          }
        },
        "clusterType": {
          "type": "keyword"
        },
        "clusterStatus": {
          "type": "keyword"
        },
        "region": {
          "type": "keyword"
        },
        "appId": {
          "type": "keyword"
        },
        "version": {
          "type": "keyword"
        },
        "accountName": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword"
            }
          }
        },
        "accountType": {
          "type": "keyword"
        },
        "accountLevel": {
          "type": "keyword"
        },
        "isBigUser": {
          "type": "integer"
        },
        "uin": {
          "type": "keyword"
        },
        "subAccountUin": {
          "type": "keyword"
        },
        "clusterLevel": {
          "type": "keyword"
        },
        "nodeCount": {
          "type": "integer"
        },
        "cpuCount": {
          "type": "integer"
        },
        "memCount": {
          "type": "integer"
        },
        "eksPodCount": {
          "type": "integer"
        },
        "vpcId": {
          "type": "keyword"
        },
        "clusterCIDR": {
          "type": "keyword"
        },
        "serviceCIDR": {
          "type": "keyword"
        },
        "metaClusterId": {
          "type": "keyword"
        },
        "deployAddonVersion": {
          "type": "object",
          "properties": {
            "name": {
              "type": "keyword"
            },
            "image": {
              "type": "keyword"
            },
            "tag": {
              "type": "keyword"
            },
            "args": {
              "type": "keyword"
            },
            "type": {
              "type": "keyword"
            },
            "source": {
              "type": "keyword"
            }
          }
        },
        "tags": {
          "type": "flattened"
        },
        "cmdbMd": {
          "type": "flattened"
        },
        "date": {
          "type": "keyword"
        },
        "createdAt": {
          "type": "date"
        },
        "updatedAt": {
          "type": "date"
        },
        "rawData": {
          "type": "object",
          "enabled": false
        }
      }
    }
  }
}'

# 创建索引模板
curl -s -X PUT \
  -u "${ES_USERNAME}:${ES_PASSWORD}" \
  -H "Content-Type: application/json" \
  "http://${ES_HOST}:${ES_PORT}/_index_template/${INDEX_PREFIX}-cluster-template" \
  -d "${INDEX_TEMPLATE}" | jq .

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 索引模板创建成功${NC}"
else
    echo -e "${RED}✗ 索引模板创建失败${NC}"
    exit 1
fi

# 创建当前日期的索引（示例）
CURRENT_DATE=$(date +%Y-%m-%d)
INDEX_NAME="${INDEX_PREFIX}-cluster-${CURRENT_DATE}"

echo -e "${YELLOW}创建示例索引: ${INDEX_NAME}${NC}"

curl -s -X PUT \
  -u "${ES_USERNAME}:${ES_PASSWORD}" \
  "http://${ES_HOST}:${ES_PORT}/${INDEX_NAME}" | jq .

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 示例索引创建成功${NC}"
else
    echo -e "${RED}✗ 示例索引创建失败${NC}"
fi

# 验证索引模板
echo -e "${YELLOW}验证索引模板...${NC}"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" \
  "http://${ES_HOST}:${ES_PORT}/_index_template/${INDEX_PREFIX}-cluster-template" | jq .name

# 显示索引列表
echo -e "${YELLOW}当前索引列表:${NC}"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" \
  "http://${ES_HOST}:${ES_PORT}/_cat/indices/${INDEX_PREFIX}-cluster-*?v"

echo ""
echo -e "${GREEN}索引初始化完成！${NC}"
echo ""
echo "索引模板名称: ${INDEX_PREFIX}-cluster-template"
echo "索引模式: ${INDEX_PREFIX}-cluster-YYYY-MM-DD"
echo "示例索引: ${INDEX_NAME}"
echo ""
echo "使用方式:"
echo "1. 程序会自动按日期创建索引"
echo "2. 索引格式: ${INDEX_PREFIX}-cluster-2025-06-29"
echo "3. 所有索引都会使用相同的映射模板"
