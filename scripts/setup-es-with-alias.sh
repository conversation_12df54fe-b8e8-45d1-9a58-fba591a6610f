#!/bin/bash

# ES索引和别名设置脚本 - 支持带日期索引和别名查询

set -e

ES_HOST=${ES_HOST:-"************"}
ES_PORT=${ES_PORT:-"9200"}
ES_USERNAME=${ES_USERNAME:-"elastic"}
ES_PASSWORD=${ES_PASSWORD:-"G08bMcIwjL"}

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}ES索引和别名设置 - 支持高性能日期索引${NC}"
echo "ES地址: ${ES_HOST}:${ES_PORT}"
echo ""

# 检查ES连接
echo -e "${YELLOW}检查Elasticsearch连接...${NC}"
if ! curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://${ES_HOST}:${ES_PORT}/_cluster/health" > /dev/null; then
    echo -e "${RED}错误: 无法连接到Elasticsearch${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Elasticsearch连接正常${NC}"

# 获取当前日期
CURRENT_DATE=$(date +%Y-%m-%d)
echo -e "${BLUE}当前日期: ${CURRENT_DATE}${NC}"

# 创建索引模板函数
create_index_template() {
    local template_name=$1
    local index_pattern=$2
    
    echo -e "${YELLOW}创建索引模板: ${template_name}...${NC}"
    
    curl -s -X PUT \
      -u "${ES_USERNAME}:${ES_PASSWORD}" \
      -H "Content-Type: application/json" \
      "http://${ES_HOST}:${ES_PORT}/_index_template/${template_name}" \
      -d '{
        "index_patterns": ["'${index_pattern}'"],
        "template": {
          "settings": {
            "number_of_shards": 3,
            "number_of_replicas": 1,
            "index.refresh_interval": "30s",
            "index.max_result_window": 50000
          },
          "mappings": {
            "properties": {
              "clusterId": {"type": "keyword"},
              "clusterName": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
              "clusterType": {"type": "keyword"},
              "clusterStatus": {"type": "keyword"},
              "region": {"type": "keyword"},
              "appId": {"type": "keyword"},
              "version": {"type": "keyword"},
              "accountName": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
              "accountType": {"type": "keyword"},
              "accountLevel": {"type": "keyword"},
              "isBigUser": {"type": "integer"},
              "uin": {"type": "keyword"},
              "subAccountUin": {"type": "keyword"},
              "clusterLevel": {"type": "keyword"},
              "nodeCount": {"type": "integer"},
              "cpuCount": {"type": "integer"},
              "memCount": {"type": "integer"},
              "eksPodCount": {"type": "integer"},
              "vpcId": {"type": "keyword"},
              "clusterCIDR": {"type": "keyword"},
              "serviceCIDR": {"type": "keyword"},
              "metaClusterId": {"type": "keyword"},
              "deployAddonVersion": {"type": "object"},
              "tags": {"type": "flattened"},
              "cmdbMd": {"type": "flattened"},
              "date": {"type": "keyword"},
              "createdAt": {"type": "date"},
              "updatedAt": {"type": "date"},
              "rawData": {"type": "object", "enabled": false}
            }
          }
        }
      }' > /dev/null

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 索引模板 ${template_name} 创建成功${NC}"
    else
        echo -e "${RED}✗ 索引模板 ${template_name} 创建失败${NC}"
        exit 1
    fi
}

# 创建别名函数
create_alias() {
    local alias_name=$1
    local index_pattern=$2
    
    echo -e "${YELLOW}创建别名: ${alias_name} -> ${index_pattern}...${NC}"
    
    curl -s -X POST \
      -u "${ES_USERNAME}:${ES_PASSWORD}" \
      -H "Content-Type: application/json" \
      "http://${ES_HOST}:${ES_PORT}/_aliases" \
      -d '{
        "actions": [
          {
            "add": {
              "index": "'${index_pattern}'",
              "alias": "'${alias_name}'"
            }
          }
        ]
      }' > /dev/null

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 别名 ${alias_name} 创建成功${NC}"
    else
        echo -e "${YELLOW}⚠️  别名 ${alias_name} 可能已存在${NC}"
    fi
}

# 创建当天索引函数
create_daily_index() {
    local index_name=$1
    
    echo -e "${YELLOW}创建当天索引: ${index_name}...${NC}"
    
    # 检查索引是否已存在
    if curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://${ES_HOST}:${ES_PORT}/${index_name}" | grep -q "index_not_found_exception"; then
        echo -e "${BLUE}索引 ${index_name} 不存在，将自动创建${NC}"
    else
        echo -e "${GREEN}✓ 索引 ${index_name} 已存在${NC}"
    fi
}

# 1. 创建TKE索引模板
create_index_template "tke-cluster-template" "tke-cluster-*"

# 2. 创建EKS索引模板
create_index_template "eks-cluster-template" "eks-cluster-*"

# 3. 创建别名（指向所有日期索引）
create_alias "tke-cluster" "tke-cluster-*"
create_alias "eks-cluster" "eks-cluster-*"

# 4. 创建当天的索引（如果不存在）
create_daily_index "tke-cluster-${CURRENT_DATE}"
create_daily_index "eks-cluster-${CURRENT_DATE}"

# 5. 验证设置
echo -e "${YELLOW}验证索引和别名设置...${NC}"

echo ""
echo -e "${BLUE}=== 当前索引列表 ===${NC}"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" \
  "http://${ES_HOST}:${ES_PORT}/_cat/indices/*cluster*?v&s=index"

echo ""
echo -e "${BLUE}=== 别名映射 ===${NC}"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" \
  "http://${ES_HOST}:${ES_PORT}/_cat/aliases/tke-cluster,eks-cluster?v"

echo ""
echo -e "${BLUE}=== 索引模板 ===${NC}"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" \
  "http://${ES_HOST}:${ES_PORT}/_cat/templates/*cluster*?v"

echo ""
echo -e "${GREEN}ES索引和别名设置完成！${NC}"
echo ""
echo -e "${BLUE}架构说明:${NC}"
echo "1. 实际索引: tke-cluster-YYYY-MM-DD, eks-cluster-YYYY-MM-DD"
echo "2. 别名查询: tke-cluster, eks-cluster (指向所有日期索引)"
echo "3. SQL查询支持: SELECT * FROM \"tke-cluster\" WHERE date = '${CURRENT_DATE}'"
echo ""
echo -e "${BLUE}性能优势:${NC}"
echo "• 日期索引: 每天独立索引，查询速度快"
echo "• 别名查询: 支持跨日期查询和SQL"
echo "• 自动创建: 新日期索引自动应用模板"
echo ""
echo -e "${BLUE}使用示例:${NC}"
echo "# 运行同步程序"
echo "./bin/cmdb-metadata-center -config config/config.yaml -date ${CURRENT_DATE}"
echo ""
echo "# SQL查询示例"
echo "curl -u \"${ES_USERNAME}:${ES_PASSWORD}\" \\"
echo "  \"http://${ES_HOST}:${ES_PORT}/_sql\" \\"
echo "  -d '{\"query\": \"SELECT clusterId, clusterName FROM \\\"tke-cluster\\\" WHERE date = \\\"${CURRENT_DATE}\\\" LIMIT 10\"}'"
