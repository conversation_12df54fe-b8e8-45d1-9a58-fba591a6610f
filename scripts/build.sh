#!/bin/bash

# CMDB元数据中心构建脚本

set -e

# 项目信息
PROJECT_NAME="cmdb-metadata-center"
VERSION=${VERSION:-$(git describe --tags --always --dirty 2>/dev/null || echo "dev")}
BUILD_TIME=$(date '+%Y-%m-%d %H:%M:%S')
GO_VERSION=$(go version | awk '{print $3}')

# 构建标志
LDFLAGS="-X main.Version=${VERSION} -X main.BuildTime=${BUILD_TIME} -X main.GoVersion=${GO_VERSION}"

echo "构建 ${PROJECT_NAME}"
echo "版本: ${VERSION}"
echo "构建时间: ${BUILD_TIME}"
echo "Go版本: ${GO_VERSION}"

# 创建输出目录
mkdir -p bin

# 构建主程序
echo "构建主程序..."
go build -ldflags "${LDFLAGS}" -o bin/${PROJECT_NAME} cmd/main.go

# 构建SCF版本
echo "构建SCF版本..."
GOOS=linux GOARCH=amd64 go build -ldflags "${LDFLAGS}" -o bin/main cmd/main.go

# 创建SCF部署包
echo "创建SCF部署包..."
cd bin
zip -r ${PROJECT_NAME}-scf.zip main
cd ..

echo "构建完成!"
echo "输出文件:"
echo "  - bin/${PROJECT_NAME} (本地可执行文件)"
echo "  - bin/main (Linux可执行文件)"
echo "  - bin/${PROJECT_NAME}-scf.zip (SCF部署包)"
