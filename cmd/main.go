package main

import (
	"context"
	"flag"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"git.woa.com/kmetis/cmdb/internal/config"
	"git.woa.com/kmetis/cmdb/internal/service/cluster"
	"git.woa.com/kmetis/cmdb/pkg/logger"

	_ "github.com/ClickHouse/clickhouse-go/v2"
	_ "github.com/go-sql-driver/mysql"
)

var (
	configPath = flag.String("config", "config/config.yaml", "配置文件路径")
	syncDate   = flag.String("date", "", "同步指定日期数据 (格式: 2025-06-29)")
	dryRun     = flag.Bool("dry-run", false, "试运行模式，不实际写入数据")
)

func main() {
	flag.Parse()

	// 加载配置
	cfg, err := config.Load(*configPath)
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化日志
	logger := logger.NewLogger(cfg.Logging)

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 初始化集群同步服务
	syncService, err := cluster.NewSyncService(cfg, logger)
	if err != nil {
		logger.Fatalf("初始化集群同步服务失败: %v", err)
	}

	// 处理信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigChan
		logger.Info("收到退出信号，正在优雅关闭...")
		cancel()
	}()

	// 执行同步
	if *syncDate != "" {
		// 同步指定日期
		date, err := time.Parse("2006-01-02", *syncDate)
		if err != nil {
			logger.Fatalf("日期格式错误: %v", err)
		}

		logger.Infof("开始同步日期: %s", date.Format("2006-01-02"))
		if err := syncService.SyncByDate(ctx, date, *dryRun); err != nil {
			logger.Fatalf("同步失败: %v", err)
		}
		logger.Info("同步完成")
	} else {
		// 同步今天的数据
		today := time.Now()
		logger.Infof("开始同步今日数据: %s", today.Format("2006-01-02"))
		if err := syncService.SyncByDate(ctx, today, *dryRun); err != nil {
			logger.Fatalf("同步失败: %v", err)
		}
		logger.Info("同步完成")
	}
}
