package main

import (
	"context"
	"flag"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"git.woa.com/kmetis/cmdb/internal/config"
	"git.woa.com/kmetis/cmdb/internal/repository/elasticsearch"
	httpService "git.woa.com/kmetis/cmdb/internal/service/http"
	"git.woa.com/kmetis/cmdb/pkg/logger"
)

func main() {
	// 解析命令行参数
	var (
		configPath = flag.String("config", "config/config.yaml", "配置文件路径")
		addr       = flag.String("addr", ":8080", "HTTP服务监听地址")
	)
	flag.Parse()

	// 加载配置
	cfg, err := config.Load(*configPath)
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化日志
	logger := logger.NewLogger(cfg.Logging)

	logger.Infof("启动CMDB HTTP服务器")
	logger.Infof("配置文件: %s", *configPath)
	logger.Infof("监听地址: %s", *addr)

	// 初始化Elasticsearch客户端
	esClient, err := elasticsearch.NewClient(cfg.Elasticsearch, logger)
	if err != nil {
		logger.Fatalf("创建Elasticsearch客户端失败: %v", err)
	}

	logger.Info("Elasticsearch客户端创建成功")

	// 创建HTTP服务器
	server := httpService.NewServer(esClient, logger)

	// 启动服务器
	go func() {
		logger.Infof("HTTP服务器启动在 %s", *addr)
		if err := server.Start(*addr); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("HTTP服务器启动失败: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("收到关闭信号，开始优雅关闭...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		logger.Errorf("服务器关闭失败: %v", err)
	}

	logger.Info("服务器已关闭")
}
