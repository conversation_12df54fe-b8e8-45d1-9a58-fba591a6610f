package handler

import (
	"git.woa.com/kmetis/cmdb/internal/config"
	"git.woa.com/kmetis/cmdb/internal/repository/elasticsearch"
	"git.woa.com/kmetis/cmdb/internal/service/tag"
	"git.woa.com/kmetis/cmdb/pkg/logger"
	"github.com/gin-gonic/gin"
)

// Router HTTP路由器
type Router struct {
	engine     *gin.Engine
	tagHandler *TagHandler
	logger     logger.Logger
}

// NewRouter 创建路由器
func NewRouter(cfg *config.Config, logger logger.Logger) (*Router, error) {
	// 创建ES客户端
	esClient, err := elasticsearch.NewClient(cfg.Elasticsearch, logger)
	if err != nil {
		return nil, err
	}

	// 创建标签服务
	tagService := tag.NewService(esClient, logger)

	// 创建处理器
	tagHandler := NewTagHandler(tagService, logger)

	// 创建Gin引擎
	engine := gin.New()

	// 添加中间件
	engine.Use(gin.Logger())
	engine.Use(gin.Recovery())
	engine.Use(corsMiddleware())

	router := &Router{
		engine:     engine,
		tagHandler: tagHandler,
		logger:     logger,
	}

	// 设置路由
	router.setupRoutes()

	return router, nil
}

// setupRoutes 设置路由
func (r *Router) setupRoutes() {
	// API版本组
	v1 := r.engine.Group("/api/v1")

	// 健康检查
	v1.GET("/health", r.healthCheck)

	// 标签管理路由
	r.setupTagRoutes(v1)
}

// setupTagRoutes 设置标签路由
func (r *Router) setupTagRoutes(rg *gin.RouterGroup) {
	// 集群标签管理
	clusters := rg.Group("/clusters")
	{
		// 获取集群标签
		clusters.GET("/:clusterType/:clusterID/tags", r.tagHandler.GetTags)

		// 设置集群标签（覆盖）
		clusters.PUT("/:clusterType/:clusterID/tags", r.tagHandler.SetTags)

		// 更新集群标签（合并）
		clusters.PATCH("/:clusterType/:clusterID/tags", r.tagHandler.UpdateTags)

		// 删除所有标签
		clusters.DELETE("/:clusterType/:clusterID/tags", r.tagHandler.DeleteAllTags)

		// 删除指定标签
		clusters.DELETE("/:clusterType/:clusterID/tags/:tagKey", r.tagHandler.DeleteTag)

		// 获取所有标签键
		clusters.GET("/:clusterType/tags/keys", r.tagHandler.ListTagKeys)

		// 获取指定标签键的所有值
		clusters.GET("/:clusterType/tags/keys/:tagKey/values", r.tagHandler.ListTagValues)

		// 获取标签统计信息
		clusters.GET("/:clusterType/tags/statistics", r.tagHandler.GetTagStatistics)

		// 批量更新标签
		clusters.POST("/tags/batch", r.tagHandler.BatchUpdateTags)
	}
}

// healthCheck 健康检查
func (r *Router) healthCheck(c *gin.Context) {
	c.JSON(200, gin.H{
		"status":  "ok",
		"service": "git.woa.com/kmetis/cmdb",
		"version": "1.0.0",
	})
}

// corsMiddleware CORS中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, PATCH, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// Run 启动HTTP服务器
func (r *Router) Run(addr string) error {
	r.logger.Infof("启动HTTP服务器，监听地址: %s", addr)
	return r.engine.Run(addr)
}

// GetEngine 获取Gin引擎（用于测试）
func (r *Router) GetEngine() *gin.Engine {
	return r.engine
}
