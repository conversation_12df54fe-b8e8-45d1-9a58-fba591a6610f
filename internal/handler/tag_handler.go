package handler

import (
	"net/http"

	"git.woa.com/kmetis/cmdb/internal/service/tag"
	"git.woa.com/kmetis/cmdb/pkg/logger"
	"github.com/gin-gonic/gin"
)

// TagHandler 标签处理器
type TagHandler struct {
	tagService *tag.Service
	logger     logger.Logger
}

// NewTagHandler 创建标签处理器
func NewTagHandler(tagService *tag.Service, logger logger.Logger) *TagHandler {
	return &TagHandler{
		tagService: tagService,
		logger:     logger,
	}
}

// GetTags 获取集群标签
// GET /api/v1/clusters/:clusterType/:clusterID/tags
func (h *TagHandler) GetTags(c *gin.Context) {
	clusterType := c.Param("clusterType")
	clusterID := c.Param("clusterID")

	if clusterType == "" || clusterID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "clusterType和clusterID不能为空",
		})
		return
	}

	resp, err := h.tagService.GetTags(c.Request.Context(), clusterID, clusterType)
	if err != nil {
		h.logger.Errorf("获取集群标签失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": resp,
	})
}

// SetTags 设置集群标签（覆盖）
// PUT /api/v1/clusters/:clusterType/:clusterID/tags
func (h *TagHandler) SetTags(c *gin.Context) {
	clusterType := c.Param("clusterType")
	clusterID := c.Param("clusterID")

	var req tag.TagRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 设置路径参数
	req.ClusterID = clusterID
	req.ClusterType = clusterType

	resp, err := h.tagService.SetTags(c.Request.Context(), &req)
	if err != nil {
		h.logger.Errorf("设置集群标签失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": resp,
	})
}

// UpdateTags 更新集群标签（合并）
// PATCH /api/v1/clusters/:clusterType/:clusterID/tags
func (h *TagHandler) UpdateTags(c *gin.Context) {
	clusterType := c.Param("clusterType")
	clusterID := c.Param("clusterID")

	var req tag.TagRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 设置路径参数
	req.ClusterID = clusterID
	req.ClusterType = clusterType

	resp, err := h.tagService.UpdateTags(c.Request.Context(), &req)
	if err != nil {
		h.logger.Errorf("更新集群标签失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": resp,
	})
}

// DeleteTag 删除指定标签
// DELETE /api/v1/clusters/:clusterType/:clusterID/tags/:tagKey
func (h *TagHandler) DeleteTag(c *gin.Context) {
	clusterType := c.Param("clusterType")
	clusterID := c.Param("clusterID")
	tagKey := c.Param("tagKey")

	if tagKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "tagKey不能为空",
		})
		return
	}

	resp, err := h.tagService.DeleteTag(c.Request.Context(), clusterID, clusterType, tagKey)
	if err != nil {
		h.logger.Errorf("删除集群标签失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": resp,
	})
}

// DeleteAllTags 删除所有标签
// DELETE /api/v1/clusters/:clusterType/:clusterID/tags
func (h *TagHandler) DeleteAllTags(c *gin.Context) {
	clusterType := c.Param("clusterType")
	clusterID := c.Param("clusterID")

	resp, err := h.tagService.DeleteAllTags(c.Request.Context(), clusterID, clusterType)
	if err != nil {
		h.logger.Errorf("删除所有集群标签失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": resp,
	})
}

// ListTagKeys 获取所有标签键
// GET /api/v1/clusters/:clusterType/tags/keys
func (h *TagHandler) ListTagKeys(c *gin.Context) {
	clusterType := c.Param("clusterType")

	keys, err := h.tagService.ListTagKeys(c.Request.Context(), clusterType)
	if err != nil {
		h.logger.Errorf("获取标签键失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": keys,
	})
}

// ListTagValues 获取指定标签键的所有值
// GET /api/v1/clusters/:clusterType/tags/keys/:tagKey/values
func (h *TagHandler) ListTagValues(c *gin.Context) {
	clusterType := c.Param("clusterType")
	tagKey := c.Param("tagKey")

	if tagKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "tagKey不能为空",
		})
		return
	}

	values, err := h.tagService.ListTagValues(c.Request.Context(), clusterType, tagKey)
	if err != nil {
		h.logger.Errorf("获取标签值失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": values,
	})
}

// BatchUpdateTags 批量更新标签
// POST /api/v1/clusters/tags/batch
func (h *TagHandler) BatchUpdateTags(c *gin.Context) {
	var requests []*tag.TagRequest
	if err := c.ShouldBindJSON(&requests); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误: " + err.Error(),
		})
		return
	}

	responses, err := h.tagService.BatchUpdateTags(c.Request.Context(), requests)
	if err != nil {
		h.logger.Errorf("批量更新标签失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": responses,
	})
}

// GetTagStatistics 获取标签统计信息
// GET /api/v1/clusters/:clusterType/tags/statistics
func (h *TagHandler) GetTagStatistics(c *gin.Context) {
	clusterType := c.Param("clusterType")

	stats, err := h.tagService.GetTagStatistics(c.Request.Context(), clusterType)
	if err != nil {
		h.logger.Errorf("获取标签统计失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": stats,
	})
}
