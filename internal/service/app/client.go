package app

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"git.woa.com/kmetis/cmdb/internal/config"
	"git.woa.com/kmetis/cmdb/pkg/logger"
)

// Client App信息采集客户端
type Client struct {
	db     *sql.DB
	logger logger.Logger
}

// AppInfo App信息
type AppInfo struct {
	AddonName     string `json:"addonName"`
	AddonVersion  string `json:"addonVersion"`
	AddonType     string `json:"addonType"`
	AddonStatus   string `json:"addonStatus"`
	AddonLocation string `json:"addonLocation"`
}

// NewClient 创建App信息采集客户端
func NewClient(cfg config.MySQLConfig, logger logger.Logger) (*Client, error) {
	// 构建MySQL连接字符串
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		cfg.Username, cfg.Password, cfg.Host, cfg.Port, cfg.Database)

	// 连接数据库
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("连接MySQL失败: %w", err)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(cfg.MaxOpenConns)
	db.SetMaxIdleConns(cfg.MaxIdleConns)
	if cfg.ConnMaxLifetime != "" {
		if duration, err := time.ParseDuration(cfg.ConnMaxLifetime); err == nil {
			db.SetConnMaxLifetime(duration)
		}
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("MySQL连接测试失败: %w", err)
	}

	logger.Info("MySQL连接成功")

	return &Client{
		db:     db,
		logger: logger,
	}, nil
}

// GetClusterApps 获取集群的App信息
func (c *Client) GetClusterApps(ctx context.Context, clusterID string) (map[string]AppInfo, error) {
	sql := `
		SELECT app_name, chart_version, app_type, app_status, app_meta
		FROM tke_app_stat 
		WHERE cluster_id = ?
		ORDER BY app_name
	`

	c.logger.Debugf("查询集群 %s 的App信息", clusterID)

	rows, err := c.db.QueryContext(ctx, sql, clusterID)
	if err != nil {
		return nil, fmt.Errorf("查询集群App信息失败: %w", err)
	}
	defer rows.Close()

	result := make(map[string]AppInfo)

	for rows.Next() {
		var appName, chartVersion, appType, appStatus, appMeta string

		if err := rows.Scan(&appName, &chartVersion, &appType, &appStatus, &appMeta); err != nil {
			c.logger.Errorf("扫描App信息失败: %v", err)
			continue
		}

		// 构建AppInfo
		appInfo := AppInfo{
			AddonName:     appName,
			AddonVersion:  chartVersion,
			AddonType:     appType,
			AddonStatus:   appStatus,
			AddonLocation: appMeta,
		}

		result[appName] = appInfo
		c.logger.Debugf("收集到App: %s, 版本: %s, 类型: %s, 状态: %s",
			appName, chartVersion, appType, appStatus)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历App信息结果失败: %w", err)
	}

	c.logger.Infof("集群 %s 收集到 %d 个App", clusterID, len(result))
	return result, nil
}

// GetBatchClusterApps 批量获取多个集群的App信息
func (c *Client) GetBatchClusterApps(ctx context.Context, clusterIDs []string) (map[string]map[string]AppInfo, error) {
	if len(clusterIDs) == 0 {
		return make(map[string]map[string]AppInfo), nil
	}

	// 构建批量查询SQL
	placeholders := make([]string, len(clusterIDs))
	args := make([]interface{}, len(clusterIDs))

	for i, clusterID := range clusterIDs {
		placeholders[i] = "?"
		args[i] = clusterID
	}

	sql := fmt.Sprintf(`
		SELECT cluster_id, app_name, chart_version, app_type, app_status, app_meta
		FROM tke_app_stat 
		WHERE cluster_id IN (%s)
		ORDER BY cluster_id, app_name
	`, strings.Join(placeholders, ","))

	c.logger.Debugf("批量查询 %d 个集群的App信息", len(clusterIDs))

	start := time.Now()
	rows, err := c.db.QueryContext(ctx, sql, args...)
	if err != nil {
		return nil, fmt.Errorf("批量查询集群App信息失败: %w", err)
	}
	defer rows.Close()

	result := make(map[string]map[string]AppInfo)
	totalApps := 0

	for rows.Next() {
		var clusterID, appName, chartVersion, appType, appStatus, appMeta string

		if err := rows.Scan(&clusterID, &appName, &chartVersion, &appType, &appStatus, &appMeta); err != nil {
			c.logger.Errorf("扫描App信息失败: %v", err)
			continue
		}

		// 初始化集群的App map
		if result[clusterID] == nil {
			result[clusterID] = make(map[string]AppInfo)
		}

		// 构建AppInfo
		appInfo := AppInfo{
			AddonName:     appName,
			AddonVersion:  chartVersion,
			AddonType:     appType,
			AddonStatus:   appStatus,
			AddonLocation: appMeta,
		}

		result[clusterID][appName] = appInfo
		totalApps++
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历App信息结果失败: %w", err)
	}

	duration := time.Since(start)
	c.logger.Infof("批量App信息查询完成: 查询耗时=%v, 集群数量=%d, 总App数=%d",
		duration, len(clusterIDs), totalApps)

	// 为没有App的集群设置空map
	for _, clusterID := range clusterIDs {
		if result[clusterID] == nil {
			result[clusterID] = make(map[string]AppInfo)
		}
	}

	return result, nil
}

// Close 关闭连接
func (c *Client) Close() error {
	if c.db != nil {
		return c.db.Close()
	}
	return nil
}

// TestConnection 测试连接
func (c *Client) TestConnection() error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return c.db.PingContext(ctx)
}
