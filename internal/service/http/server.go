package http

import (
	"context"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"git.woa.com/kmetis/cmdb/internal/repository/elasticsearch"
	"git.woa.com/kmetis/cmdb/pkg/logger"
)

// Server HTTP服务器
type Server struct {
	router     *gin.Engine
	tagService *TagService
	logger     logger.Logger
}

// NewServer 创建HTTP服务器
func NewServer(esClient *elasticsearch.Client, logger logger.Logger) *Server {
	// 设置Gin模式
	gin.SetMode(gin.ReleaseMode)

	router := gin.New()
	router.Use(gin.Logger(), gin.Recovery())

	tagService := NewTagService(esClient, logger)

	server := &Server{
		router:     router,
		tagService: tagService,
		logger:     logger,
	}

	server.setupRoutes()
	return server
}

// setupRoutes 设置路由
func (s *Server) setupRoutes() {
	// 健康检查
	s.router.GET("/health", s.healthCheck)

	// API路由组
	api := s.router.Group("/api/v1")
	{
		// 标签管理
		tags := api.Group("/tags")
		{
			// 根据AppID批量打标签
			tags.POST("/batch/appid", s.tagService.BatchTagByAppID)

			// 根据集群ID列表批量打标签
			tags.POST("/batch/clusters", s.tagService.BatchTagByClusters)

			// 根据AppID批量删除标签
			tags.DELETE("/batch/appid", s.tagService.DeleteTagsByAppID)

			// 获取集群标签
			tags.GET("/:clusterType/:clusterID", s.tagService.GetClusterTags)
		}

		// 集群查询
		clusters := api.Group("/clusters")
		{
			// 根据条件查询集群列表
			clusters.POST("/search", s.tagService.QueryClusters)

			// 快速查询接口（GET方式）
			clusters.GET("/search", s.quickSearchClusters)
		}
	}
}

// healthCheck 健康检查
func (s *Server) healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "ok",
		"timestamp": time.Now().Unix(),
		"service":   "git.woa.com/kmetis/cmdb",
	})
}

// quickSearchClusters 快速查询集群（GET方式）
func (s *Server) quickSearchClusters(c *gin.Context) {
	// 从查询参数构建请求
	req := QueryClustersRequest{
		AppID:       c.Query("appId"),
		ClusterType: c.Query("clusterType"),
		Region:      c.Query("region"),
		Uin:         c.Query("uin"),
		AccountName: c.Query("accountName"),
	}

	// 解析limit和offset
	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil {
			req.Limit = limit
		}
	}
	if offsetStr := c.Query("offset"); offsetStr != "" {
		if offset, err := strconv.Atoi(offsetStr); err == nil {
			req.Offset = offset
		}
	}

	// 解析标签参数 (格式: tag.key=value)
	req.Tags = make(map[string]string)
	for key, values := range c.Request.URL.Query() {
		if strings.HasPrefix(key, "tag.") && len(values) > 0 {
			tagKey := strings.TrimPrefix(key, "tag.")
			req.Tags[tagKey] = values[0]
		}
	}

	// 设置默认值
	if req.Limit <= 0 {
		req.Limit = 100
	}

	s.logger.Infof("快速查询集群: AppID=%s, ClusterType=%s, Region=%s, 标签数量=%d",
		req.AppID, req.ClusterType, req.Region, len(req.Tags))

	// 执行搜索
	clusters, total, err := s.tagService.searchClusters(c.Request.Context(), req)
	if err != nil {
		s.logger.Errorf("搜索集群失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "搜索集群失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"clusters": clusters,
		"total":    total,
		"limit":    req.Limit,
		"offset":   req.Offset,
	})
}

// Start 启动服务器
func (s *Server) Start(addr string) error {
	s.logger.Infof("启动HTTP服务器: %s", addr)
	return s.router.Run(addr)
}

// Shutdown 优雅关闭服务器
func (s *Server) Shutdown(ctx context.Context) error {
	s.logger.Info("关闭HTTP服务器")
	// Gin没有内置的优雅关闭，这里只是记录日志
	return nil
}
