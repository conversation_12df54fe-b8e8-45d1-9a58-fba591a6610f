package http

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"git.woa.com/kmetis/cmdb/internal/model"
	"git.woa.com/kmetis/cmdb/internal/repository/elasticsearch"
	"git.woa.com/kmetis/cmdb/pkg/logger"
)

// TagService 标签管理服务
type TagService struct {
	esClient *elasticsearch.Client
	logger   logger.Logger
}

// NewTagService 创建标签服务
func NewTagService(esClient *elasticsearch.Client, logger logger.Logger) *TagService {
	return &TagService{
		esClient: esClient,
		logger:   logger,
	}
}

// BatchTagByAppIDRequest 根据AppID批量打标签请求
type BatchTagByAppIDRequest struct {
	AppID       string            `json:"appId" binding:"required"`
	Tags        map[string]string `json:"tags" binding:"required"`
	ClusterType string            `json:"clusterType,omitempty"` // 可选：tke, eks，为空则对所有类型生效
}

// BatchTagByClusterRequest 根据集群ID批量打标签请求
type BatchTagByClusterRequest struct {
	ClusterIDs []string          `json:"clusterIds" binding:"required"`
	Tags       map[string]string `json:"tags" binding:"required"`
}

// QueryClustersRequest 查询集群请求
type QueryClustersRequest struct {
	AppID       string            `json:"appId,omitempty"`
	ClusterType string            `json:"clusterType,omitempty"` // tke, eks
	Region      string            `json:"region,omitempty"`
	Tags        map[string]string `json:"tags,omitempty"`
	Uin         string            `json:"uin,omitempty"`
	AccountName string            `json:"accountName,omitempty"`
	Limit       int               `json:"limit,omitempty"`
	Offset      int               `json:"offset,omitempty"`
}

// DeleteTagByAppIDRequest 根据AppID删除标签请求
type DeleteTagByAppIDRequest struct {
	AppID       string   `json:"appId" binding:"required"`
	TagKeys     []string `json:"tagKeys" binding:"required"`
	ClusterType string   `json:"clusterType,omitempty"` // 可选：tke, eks，为空则对所有类型生效
}

// ClusterInfo 集群信息
type ClusterInfo struct {
	ClusterID   string            `json:"clusterId"`
	ClusterName string            `json:"clusterName"`
	ClusterType string            `json:"clusterType"`
	Region      string            `json:"region"`
	AppID       string            `json:"appId"`
	Uin         string            `json:"uin"`
	AccountName string            `json:"accountName"`
	Tags        map[string]string `json:"tags"`
	CreatedAt   time.Time         `json:"createdAt"`
}

// BatchTagByAppID 根据AppID给所有集群打标签
func (s *TagService) BatchTagByAppID(c *gin.Context) {
	var req BatchTagByAppIDRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数错误: " + err.Error()})
		return
	}

	// 验证集群类型参数
	if req.ClusterType != "" && req.ClusterType != "tke" && req.ClusterType != "eks" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "集群类型只能是 tke 或 eks"})
		return
	}

	clusterTypeDesc := "所有类型"
	if req.ClusterType != "" {
		clusterTypeDesc = req.ClusterType
	}
	s.logger.Infof("根据AppID批量打标签: AppID=%s, 集群类型=%s, 标签数量=%d", req.AppID, clusterTypeDesc, len(req.Tags))

	// 查询所有匹配的集群
	clusters, err := s.queryClustersByAppID(c.Request.Context(), req.AppID, req.ClusterType)
	if err != nil {
		s.logger.Errorf("查询AppID集群失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "查询集群失败"})
		return
	}

	if len(clusters) == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "未找到匹配的集群"})
		return
	}

	// 批量更新标签
	successCount, failCount, err := s.batchUpdateTags(c.Request.Context(), clusters, req.Tags, false)
	if err != nil {
		s.logger.Errorf("批量更新标签失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "批量更新失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":       "批量打标签完成",
		"appId":         req.AppID,
		"clusterType":   clusterTypeDesc,
		"totalClusters": len(clusters),
		"successCount":  successCount,
		"failCount":     failCount,
		"tags":          req.Tags,
	})
}

// BatchTagByClusters 根据集群ID列表打标签
func (s *TagService) BatchTagByClusters(c *gin.Context) {
	var req BatchTagByClusterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数错误: " + err.Error()})
		return
	}

	s.logger.Infof("根据集群ID批量打标签: 集群数量=%d, 标签数量=%d", len(req.ClusterIDs), len(req.Tags))

	// 查询集群信息
	clusters, err := s.queryClustersByIDs(c.Request.Context(), req.ClusterIDs)
	if err != nil {
		s.logger.Errorf("查询集群信息失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "查询集群失败"})
		return
	}

	// 批量更新标签
	successCount, failCount, err := s.batchUpdateTags(c.Request.Context(), clusters, req.Tags, false)
	if err != nil {
		s.logger.Errorf("批量更新标签失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "批量更新失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":       "批量打标签完成",
		"totalClusters": len(clusters),
		"successCount":  successCount,
		"failCount":     failCount,
		"tags":          req.Tags,
	})
}

// QueryClusters 根据条件查询集群列表
func (s *TagService) QueryClusters(c *gin.Context) {
	var req QueryClustersRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数错误: " + err.Error()})
		return
	}

	// 设置默认值
	if req.Limit <= 0 {
		req.Limit = 100
	}
	if req.Limit > 1000 {
		req.Limit = 1000
	}

	s.logger.Infof("查询集群列表: AppID=%s, ClusterType=%s, Region=%s, 标签数量=%d",
		req.AppID, req.ClusterType, req.Region, len(req.Tags))

	// 构建查询条件
	clusters, total, err := s.searchClusters(c.Request.Context(), req)
	if err != nil {
		s.logger.Errorf("搜索集群失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "搜索集群失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"clusters": clusters,
		"total":    total,
		"limit":    req.Limit,
		"offset":   req.Offset,
	})
}

// DeleteTagsByAppID 根据AppID删除指定标签
func (s *TagService) DeleteTagsByAppID(c *gin.Context) {
	var req DeleteTagByAppIDRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数错误: " + err.Error()})
		return
	}

	// 验证集群类型参数
	if req.ClusterType != "" && req.ClusterType != "tke" && req.ClusterType != "eks" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "集群类型只能是 tke 或 eks"})
		return
	}

	clusterTypeDesc := "所有类型"
	if req.ClusterType != "" {
		clusterTypeDesc = req.ClusterType
	}
	s.logger.Infof("根据AppID删除标签: AppID=%s, 集群类型=%s, 标签键=%v", req.AppID, clusterTypeDesc, req.TagKeys)

	// 查询所有匹配的集群
	clusters, err := s.queryClustersByAppID(c.Request.Context(), req.AppID, req.ClusterType)
	if err != nil {
		s.logger.Errorf("查询AppID集群失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "查询集群失败"})
		return
	}

	if len(clusters) == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "未找到匹配的集群"})
		return
	}

	// 批量删除标签
	successCount, failCount, err := s.batchDeleteTags(c.Request.Context(), clusters, req.TagKeys)
	if err != nil {
		s.logger.Errorf("批量删除标签失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "批量删除失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":       "批量删除标签完成",
		"appId":         req.AppID,
		"clusterType":   clusterTypeDesc,
		"totalClusters": len(clusters),
		"successCount":  successCount,
		"failCount":     failCount,
		"deletedKeys":   req.TagKeys,
	})
}

// GetClusterTags 获取集群标签
func (s *TagService) GetClusterTags(c *gin.Context) {
	clusterType := c.Param("clusterType")
	clusterID := c.Param("clusterID")

	if clusterType == "" || clusterID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "集群类型和集群ID不能为空"})
		return
	}

	// 获取最新的索引名
	indexName := s.esClient.GetIndexNameByType(time.Now(), clusterType)

	cluster, err := s.esClient.GetClusterByID(c.Request.Context(), indexName, clusterID)
	if err != nil {
		s.logger.Errorf("获取集群信息失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取集群信息失败"})
		return
	}

	if cluster == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "集群不存在"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"clusterID": cluster.ClusterID,
		"tags":      cluster.Tags,
	})
}

// queryClustersByAppID 根据AppID查询集群（支持指定集群类型）
func (s *TagService) queryClustersByAppID(ctx context.Context, appID string, clusterType ...string) ([]ClusterInfo, error) {
	// 构建查询条件 - 使用appId.keyword进行精确匹配
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"term": map[string]interface{}{
				"appId.keyword": appID,
			},
		},
		"size": 10000, // 最大返回数量
	}

	var allClusters []ClusterInfo

	// 确定要查询的集群类型
	var typesToQuery []string
	if len(clusterType) > 0 && clusterType[0] != "" {
		typesToQuery = []string{clusterType[0]}
	} else {
		typesToQuery = []string{"tke", "eks"} // 默认查询所有类型
	}

	for _, cType := range typesToQuery {
		// 使用写入ES的索引名（带starship前缀）
		indexName := s.esClient.GetIndexNameByType(time.Now(), cType)
		clusters, err := s.searchClustersInIndex(ctx, indexName, query)
		if err != nil {
			s.logger.Warnf("查询%s集群失败: %v", cType, err)
		} else {
			allClusters = append(allClusters, clusters...)
			s.logger.Infof("从%s索引查询到%d个AppID=%s的集群", indexName, len(clusters), appID)
		}
	}

	return allClusters, nil
}

// queryClustersByIDs 根据集群ID列表查询集群
func (s *TagService) queryClustersByIDs(ctx context.Context, clusterIDs []string) ([]ClusterInfo, error) {
	// 构建查询条件
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"terms": map[string]interface{}{
				"clusterID": clusterIDs,
			},
		},
		"size": len(clusterIDs),
	}

	var allClusters []ClusterInfo

	// 查询TKE集群
	tkeIndex := s.esClient.GetIndexNameByType(time.Now(), "tke")
	tkeClusters, err := s.searchClustersInIndex(ctx, tkeIndex, query)
	if err != nil {
		s.logger.Warnf("查询TKE集群失败: %v", err)
	} else {
		allClusters = append(allClusters, tkeClusters...)
	}

	// 查询EKS集群
	eksIndex := s.esClient.GetIndexNameByType(time.Now(), "eks")
	eksClusters, err := s.searchClustersInIndex(ctx, eksIndex, query)
	if err != nil {
		s.logger.Warnf("查询EKS集群失败: %v", err)
	} else {
		allClusters = append(allClusters, eksClusters...)
	}

	return allClusters, nil
}

// searchClusters 根据条件搜索集群
func (s *TagService) searchClusters(ctx context.Context, req QueryClustersRequest) ([]ClusterInfo, int, error) {
	// 构建查询条件
	mustQueries := []map[string]interface{}{}

	if req.AppID != "" {
		mustQueries = append(mustQueries, map[string]interface{}{
			"term": map[string]interface{}{
				"appId": req.AppID,
			},
		})
	}

	if req.Region != "" {
		mustQueries = append(mustQueries, map[string]interface{}{
			"term": map[string]interface{}{
				"region": req.Region,
			},
		})
	}

	if req.Uin != "" {
		mustQueries = append(mustQueries, map[string]interface{}{
			"term": map[string]interface{}{
				"uin": req.Uin,
			},
		})
	}

	if req.AccountName != "" {
		mustQueries = append(mustQueries, map[string]interface{}{
			"term": map[string]interface{}{
				"accountName.keyword": req.AccountName,
			},
		})
	}

	// 添加标签查询
	for key, value := range req.Tags {
		mustQueries = append(mustQueries, map[string]interface{}{
			"term": map[string]interface{}{
				fmt.Sprintf("tags.%s.keyword", key): value,
			},
		})
	}

	query := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": mustQueries,
			},
		},
		"from": req.Offset,
		"size": req.Limit,
		"sort": []map[string]interface{}{
			{
				"clusterCreatedAt": map[string]interface{}{
					"order": "desc",
				},
			},
		},
	}

	var allClusters []ClusterInfo
	var totalCount int

	// 根据集群类型查询
	if req.ClusterType == "" || req.ClusterType == "tke" {
		tkeIndex := s.esClient.GetIndexNameByType(time.Now(), "tke")
		tkeClusters, tkeTotal, err := s.searchClustersInIndexWithTotal(ctx, tkeIndex, query)
		if err != nil {
			s.logger.Warnf("查询TKE集群失败: %v", err)
		} else {
			allClusters = append(allClusters, tkeClusters...)
			totalCount += tkeTotal
		}
	}

	if req.ClusterType == "" || req.ClusterType == "eks" {
		eksIndex := s.esClient.GetIndexNameByType(time.Now(), "eks")
		eksClusters, eksTotal, err := s.searchClustersInIndexWithTotal(ctx, eksIndex, query)
		if err != nil {
			s.logger.Warnf("查询EKS集群失败: %v", err)
		} else {
			allClusters = append(allClusters, eksClusters...)
			totalCount += eksTotal
		}
	}

	return allClusters, totalCount, nil
}

// searchClustersInIndex 在指定索引中搜索集群
func (s *TagService) searchClustersInIndex(ctx context.Context, indexName string, query map[string]interface{}) ([]ClusterInfo, error) {
	clusters, _, err := s.searchClustersInIndexWithTotal(ctx, indexName, query)
	return clusters, err
}

// searchClustersInIndexWithTotal 在指定索引中搜索集群并返回总数
func (s *TagService) searchClustersInIndexWithTotal(ctx context.Context, indexName string, query map[string]interface{}) ([]ClusterInfo, int, error) {
	queryBytes, err := json.Marshal(query)
	if err != nil {
		return nil, 0, fmt.Errorf("构建查询失败: %w", err)
	}

	// 使用写入ES客户端查询（因为标签数据在写入ES中）
	res, err := s.esClient.GetWriteClient().Search(
		s.esClient.GetWriteClient().Search.WithContext(ctx),
		s.esClient.GetWriteClient().Search.WithIndex(indexName),
		s.esClient.GetWriteClient().Search.WithBody(strings.NewReader(string(queryBytes))),
	)
	if err != nil {
		return nil, 0, fmt.Errorf("执行搜索失败: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, 0, fmt.Errorf("搜索请求失败: %s", res.String())
	}

	var searchResult struct {
		Hits struct {
			Total struct {
				Value int `json:"value"`
			} `json:"total"`
			Hits []struct {
				Source model.ESClusterDocument `json:"_source"`
			} `json:"hits"`
		} `json:"hits"`
	}

	if err := json.NewDecoder(res.Body).Decode(&searchResult); err != nil {
		return nil, 0, fmt.Errorf("解析搜索结果失败: %w", err)
	}

	var clusters []ClusterInfo
	for _, hit := range searchResult.Hits.Hits {
		// 转换Tags字段
		tags := make(map[string]string)
		if hit.Source.Tags != nil {
			for k, v := range hit.Source.Tags {
				if str, ok := v.(string); ok {
					tags[k] = str
				}
			}
		}

		cluster := ClusterInfo{
			ClusterID:   hit.Source.ClusterID,
			ClusterName: hit.Source.ClusterName,
			ClusterType: hit.Source.ClusterType,
			Region:      hit.Source.Region,
			AppID:       hit.Source.AppID,
			Uin:         hit.Source.Uin,
			AccountName: hit.Source.AccountName,
			Tags:        tags,
			CreatedAt:   hit.Source.ClusterCreatedAt,
		}
		clusters = append(clusters, cluster)
	}

	return clusters, searchResult.Hits.Total.Value, nil
}

// batchUpdateTags 批量更新标签
func (s *TagService) batchUpdateTags(ctx context.Context, clusters []ClusterInfo, tags map[string]string, isDelete bool) (int, int, error) {
	successCount := 0
	failCount := 0

	for _, cluster := range clusters {
		indexName := s.esClient.GetIndexNameByType(time.Now(), cluster.ClusterType)

		// 构建更新脚本
		var script string
		var params map[string]interface{}

		if isDelete {
			// 删除标签的脚本
			var deleteKeys []string
			for key := range tags {
				deleteKeys = append(deleteKeys, key)
			}
			script = "for (key in params.keys) { ctx._source.tags.remove(key) }"
			params = map[string]interface{}{
				"keys": deleteKeys,
			}
		} else {
			// 添加/更新标签的脚本
			script = "if (ctx._source.tags == null) { ctx._source.tags = [:] } for (entry in params.tags.entrySet()) { ctx._source.tags[entry.getKey()] = entry.getValue() }"
			params = map[string]interface{}{
				"tags": tags,
			}
		}

		updateDoc := map[string]interface{}{
			"script": map[string]interface{}{
				"source": script,
				"params": params,
			},
		}

		updateBytes, err := json.Marshal(updateDoc)
		if err != nil {
			s.logger.Errorf("构建更新文档失败: %v", err)
			failCount++
			continue
		}

		res, err := s.esClient.GetWriteClient().Update(
			indexName,
			cluster.ClusterID,
			strings.NewReader(string(updateBytes)),
			s.esClient.GetWriteClient().Update.WithContext(ctx),
		)
		if err != nil {
			s.logger.Errorf("更新集群 %s 标签失败: %v", cluster.ClusterID, err)
			failCount++
			continue
		}
		res.Body.Close()

		if res.IsError() {
			s.logger.Errorf("更新集群 %s 标签请求失败: %s", cluster.ClusterID, res.String())
			failCount++
			continue
		}

		successCount++
		s.logger.Debugf("成功更新集群 %s 标签", cluster.ClusterID)
	}

	return successCount, failCount, nil
}

// batchDeleteTags 批量删除标签
func (s *TagService) batchDeleteTags(ctx context.Context, clusters []ClusterInfo, tagKeys []string) (int, int, error) {
	tags := make(map[string]string)
	for _, key := range tagKeys {
		tags[key] = "" // 值不重要，只用于删除
	}
	return s.batchUpdateTags(ctx, clusters, tags, true)
}
