package service

import (
	"testing"
	"time"

	"git.woa.com/kmetis/cmdb/internal/config"
	"github.com/stretchr/testify/assert"
)

// TestTimeOffsetConfiguration 测试时间偏移配置
func TestTimeOffsetConfiguration(t *testing.T) {
	// 测试默认配置
	cfg := &config.Config{
		Sync: config.SyncConfig{},
	}

	// 应用默认值
	if cfg.Sync.ESQueryOffset == 0 {
		cfg.Sync.ESQueryOffset = -2
	}
	if cfg.Sync.CHQueryOffset == 0 {
		cfg.Sync.CHQueryOffset = -1
	}

	assert.Equal(t, -2, cfg.Sync.ESQueryOffset, "ES查询偏移应该是-2天")
	assert.Equal(t, -1, cfg.Sync.CHQueryOffset, "ClickHouse查询偏移应该是-1天")
}

// TestDateCalculation 测试日期计算
func TestDateCalculation(t *testing.T) {
	// 基准日期：2025-06-30
	baseDate := time.Date(2025, 6, 30, 0, 0, 0, 0, time.UTC)

	// ClickHouse查询日期：2025-06-29 (前1天)
	chQueryDate := baseDate.AddDate(0, 0, -1)
	expectedCHDate := time.Date(2025, 6, 29, 0, 0, 0, 0, time.UTC)
	assert.Equal(t, expectedCHDate, chQueryDate, "ClickHouse查询日期应该是前1天")

	// ES查询日期：2025-06-28 (前2天)
	esQueryDate := baseDate.AddDate(0, 0, -2)
	expectedESDate := time.Date(2025, 6, 28, 0, 0, 0, 0, time.UTC)
	assert.Equal(t, expectedESDate, esQueryDate, "ES查询日期应该是前2天")
}

// TestDataFlowScenario 测试数据流程场景
func TestDataFlowScenario(t *testing.T) {
	// 模拟真实场景：今天是2025-06-30，需要同步数据
	syncDate := time.Date(2025, 6, 30, 0, 0, 0, 0, time.UTC)

	// 配置时间偏移
	esOffset := -2 // ES查询前2天的数据
	chOffset := -1 // ClickHouse查询前1天的数据

	// 计算实际查询日期
	chQueryDate := syncDate.AddDate(0, 0, chOffset) // 2025-06-29
	esQueryDate := syncDate.AddDate(0, 0, esOffset) // 2025-06-28

	// 验证日期
	assert.Equal(t, "2025-06-30", syncDate.Format("2006-01-02"), "同步日期")
	assert.Equal(t, "2025-06-29", chQueryDate.Format("2006-01-02"), "ClickHouse查询日期")
	assert.Equal(t, "2025-06-28", esQueryDate.Format("2006-01-02"), "ES查询日期")

	// 验证数据流程逻辑
	t.Logf("数据流程:")
	t.Logf("  同步日期: %s", syncDate.Format("2006-01-02"))
	t.Logf("  ClickHouse查询日期: %s (偏移%d天)", chQueryDate.Format("2006-01-02"), chOffset)
	t.Logf("  ES查询日期: %s (偏移%d天)", esQueryDate.Format("2006-01-02"), esOffset)
	t.Logf("  写入ES索引: cmdb-cluster-%s", syncDate.Format("2006-01-02"))
}

// TestIndexNameGeneration 测试索引名称生成
func TestIndexNameGeneration(t *testing.T) {
	testCases := []struct {
		date     time.Time
		expected string
	}{
		{
			date:     time.Date(2025, 6, 30, 0, 0, 0, 0, time.UTC),
			expected: "cmdb-cluster-2025-06-30",
		},
		{
			date:     time.Date(2025, 12, 31, 0, 0, 0, 0, time.UTC),
			expected: "cmdb-cluster-2025-12-31",
		},
		{
			date:     time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
			expected: "cmdb-cluster-2025-01-01",
		},
	}

	for _, tc := range testCases {
		indexName := "cmdb-cluster-" + tc.date.Format("2006-01-02")
		assert.Equal(t, tc.expected, indexName, "索引名称生成错误")
	}
}

// TestDynamicTagsPreservation 测试动态标签保留逻辑
func TestDynamicTagsPreservation(t *testing.T) {
	// 模拟ES现有数据中的动态标签
	existingTags := map[string]interface{}{
		"environment": "production",
		"team":        "platform",
		"customer":    "important-customer",
		"cost_center": "engineering",
		"project":     "tke-platform",
	}

	existingCmdbMd := map[string]interface{}{
		"owner":      "admin",
		"cost":       "high",
		"sla_level":  "p0",
		"backup":     true,
		"monitoring": true,
	}

	// 验证标签数量
	assert.Equal(t, 5, len(existingTags), "应该有5个动态标签")
	assert.Equal(t, 5, len(existingCmdbMd), "应该有5个CMDB元数据")

	// 验证关键标签
	assert.Equal(t, "production", existingTags["environment"])
	assert.Equal(t, "important-customer", existingTags["customer"])
	assert.Equal(t, "p0", existingCmdbMd["sla_level"])

	t.Logf("动态标签保留测试:")
	t.Logf("  Tags: %+v", existingTags)
	t.Logf("  CmdbMd: %+v", existingCmdbMd)
}

// TestConfigurationValidation 测试配置验证
func TestConfigurationValidation(t *testing.T) {
	testCases := []struct {
		name          string
		esOffset      int
		chOffset      int
		expectedValid bool
		description   string
	}{
		{
			name:          "默认配置",
			esOffset:      -2,
			chOffset:      -1,
			expectedValid: true,
			description:   "ES查询前2天，CH查询前1天",
		},
		{
			name:          "相同偏移",
			esOffset:      -1,
			chOffset:      -1,
			expectedValid: true,
			description:   "ES和CH查询同一天",
		},
		{
			name:          "ES偏移更大",
			esOffset:      -3,
			chOffset:      -1,
			expectedValid: true,
			description:   "ES查询更早的数据",
		},
		{
			name:          "正偏移",
			esOffset:      1,
			chOffset:      0,
			expectedValid: false,
			description:   "不应该查询未来的数据",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 验证偏移配置的合理性
			if tc.expectedValid {
				assert.LessOrEqual(t, tc.esOffset, 0, "ES偏移应该<=0")
				assert.LessOrEqual(t, tc.chOffset, 0, "CH偏移应该<=0")
			}
			t.Logf("%s: ES偏移=%d, CH偏移=%d", tc.description, tc.esOffset, tc.chOffset)
		})
	}
}
