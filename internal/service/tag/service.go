package tag

import (
	"context"
	"fmt"
	"time"

	"git.woa.com/kmetis/cmdb/internal/repository/elasticsearch"
	"git.woa.com/kmetis/cmdb/pkg/logger"
)

// Service 标签服务
type Service struct {
	esClient *elasticsearch.Client
	logger   logger.Logger
}

// NewService 创建标签服务
func NewService(esClient *elasticsearch.Client, logger logger.Logger) *Service {
	return &Service{
		esClient: esClient,
		logger:   logger,
	}
}

// TagRequest 标签操作请求
type TagRequest struct {
	ClusterID   string                 `json:"clusterId" binding:"required"`
	ClusterType string                 `json:"clusterType" binding:"required,oneof=tke eks"`
	Tags        map[string]interface{} `json:"tags" binding:"required"`
}

// TagResponse 标签操作响应
type TagResponse struct {
	ClusterID   string                 `json:"clusterId"`
	ClusterType string                 `json:"clusterType"`
	Tags        map[string]interface{} `json:"tags"`
	UpdatedAt   time.Time              `json:"updatedAt"`
}

// GetTags 获取集群标签
func (s *Service) GetTags(ctx context.Context, clusterID, clusterType string) (*TagResponse, error) {
	// 获取最新的集群数据
	indexName := s.getLatestIndexName(clusterType)

	doc, err := s.esClient.GetClusterByID(ctx, indexName, clusterID)
	if err != nil {
		return nil, fmt.Errorf("获取集群标签失败: %w", err)
	}

	if doc == nil {
		return nil, fmt.Errorf("集群 %s 不存在", clusterID)
	}

	return &TagResponse{
		ClusterID:   doc.ClusterID,
		ClusterType: doc.ClusterType,
		Tags:        doc.Tags,
		UpdatedAt:   doc.UpdatedAt,
	}, nil
}

// SetTags 设置集群标签（覆盖）
func (s *Service) SetTags(ctx context.Context, req *TagRequest) (*TagResponse, error) {
	return s.updateTags(ctx, req, false)
}

// UpdateTags 更新集群标签（合并）
func (s *Service) UpdateTags(ctx context.Context, req *TagRequest) (*TagResponse, error) {
	return s.updateTags(ctx, req, true)
}

// DeleteTag 删除指定标签
func (s *Service) DeleteTag(ctx context.Context, clusterID, clusterType, tagKey string) (*TagResponse, error) {
	// 获取当前标签
	current, err := s.GetTags(ctx, clusterID, clusterType)
	if err != nil {
		return nil, err
	}

	// 删除指定标签
	if current.Tags == nil {
		current.Tags = make(map[string]interface{})
	}
	delete(current.Tags, tagKey)

	// 更新标签
	req := &TagRequest{
		ClusterID:   clusterID,
		ClusterType: clusterType,
		Tags:        current.Tags,
	}

	return s.updateTags(ctx, req, false)
}

// DeleteAllTags 删除所有标签
func (s *Service) DeleteAllTags(ctx context.Context, clusterID, clusterType string) (*TagResponse, error) {
	req := &TagRequest{
		ClusterID:   clusterID,
		ClusterType: clusterType,
		Tags:        make(map[string]interface{}),
	}

	return s.updateTags(ctx, req, false)
}

// updateTags 内部标签更新方法
func (s *Service) updateTags(ctx context.Context, req *TagRequest, merge bool) (*TagResponse, error) {
	indexName := s.getLatestIndexName(req.ClusterType)

	// 获取当前文档
	doc, err := s.esClient.GetClusterByID(ctx, indexName, req.ClusterID)
	if err != nil {
		return nil, fmt.Errorf("获取集群信息失败: %w", err)
	}

	if doc == nil {
		return nil, fmt.Errorf("集群 %s 不存在", req.ClusterID)
	}

	// 更新标签
	if merge && doc.Tags != nil {
		// 合并模式：保留现有标签，更新新标签
		for key, value := range req.Tags {
			doc.Tags[key] = value
		}
	} else {
		// 覆盖模式：完全替换标签
		doc.Tags = req.Tags
	}

	// 更新时间
	doc.UpdatedAt = time.Now()

	// 保存到ES
	if err := s.esClient.UpdateClusterTags(ctx, indexName, req.ClusterID, doc.Tags, doc.UpdatedAt); err != nil {
		return nil, fmt.Errorf("更新集群标签失败: %w", err)
	}

	s.logger.Infof("成功更新集群 %s 的标签", req.ClusterID)

	return &TagResponse{
		ClusterID:   doc.ClusterID,
		ClusterType: doc.ClusterType,
		Tags:        doc.Tags,
		UpdatedAt:   doc.UpdatedAt,
	}, nil
}

// getLatestIndexName 获取最新的索引名（当天）
func (s *Service) getLatestIndexName(clusterType string) string {
	today := time.Now()
	return s.esClient.GetIndexNameByType(today, clusterType)
}

// ListTagKeys 获取所有标签键
func (s *Service) ListTagKeys(ctx context.Context, clusterType string) ([]string, error) {
	indexName := s.getLatestIndexName(clusterType)

	// 使用聚合查询获取所有标签键
	keys, err := s.esClient.GetAllTagKeys(ctx, indexName)
	if err != nil {
		return nil, fmt.Errorf("获取标签键失败: %w", err)
	}

	return keys, nil
}

// ListTagValues 获取指定标签键的所有值
func (s *Service) ListTagValues(ctx context.Context, clusterType, tagKey string) ([]interface{}, error) {
	indexName := s.getLatestIndexName(clusterType)

	// 使用聚合查询获取标签值
	values, err := s.esClient.GetTagValues(ctx, indexName, tagKey)
	if err != nil {
		return nil, fmt.Errorf("获取标签值失败: %w", err)
	}

	return values, nil
}

// BatchUpdateTags 批量更新标签
func (s *Service) BatchUpdateTags(ctx context.Context, requests []*TagRequest) ([]*TagResponse, error) {
	var responses []*TagResponse
	var errors []error

	for _, req := range requests {
		resp, err := s.UpdateTags(ctx, req)
		if err != nil {
			errors = append(errors, fmt.Errorf("集群 %s: %w", req.ClusterID, err))
			continue
		}
		responses = append(responses, resp)
	}

	if len(errors) > 0 {
		s.logger.Warnf("批量更新标签部分失败: %d 个错误", len(errors))
	}

	return responses, nil
}

// GetTagStatistics 获取标签统计信息
func (s *Service) GetTagStatistics(ctx context.Context, clusterType string) (map[string]interface{}, error) {
	indexName := s.getLatestIndexName(clusterType)

	// 构建聚合查询获取标签统计
	stats, err := s.esClient.GetTagStatistics(ctx, indexName)
	if err != nil {
		return nil, fmt.Errorf("获取标签统计失败: %w", err)
	}

	return stats, nil
}
