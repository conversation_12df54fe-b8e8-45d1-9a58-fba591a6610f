package cluster

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.woa.com/kmetis/cmdb/pkg/logger"
)

// SyncMetrics 同步性能指标
type SyncMetrics struct {
	// 基础指标
	TotalClusters    int           `json:"total_clusters"`
	ProcessedClusters int          `json:"processed_clusters"`
	NewClusters      int           `json:"new_clusters"`
	UpdatedClusters  int           `json:"updated_clusters"`
	FailedClusters   int           `json:"failed_clusters"`
	
	// 时间指标
	StartTime        time.Time     `json:"start_time"`
	EndTime          time.Time     `json:"end_time"`
	TotalDuration    time.Duration `json:"total_duration"`
	PreProcessTime   time.Duration `json:"preprocess_time"`
	ProcessingTime   time.Duration `json:"processing_time"`
	
	// 性能指标
	ClustersPerSecond float64      `json:"clusters_per_second"`
	AvgBatchTime     time.Duration `json:"avg_batch_time"`
	MaxBatchTime     time.Duration `json:"max_batch_time"`
	MinBatchTime     time.Duration `json:"min_batch_time"`
	
	// 并发指标
	ConcurrentWorkers int          `json:"concurrent_workers"`
	TotalBatches     int           `json:"total_batches"`
	CompletedBatches int           `json:"completed_batches"`
	FailedBatches    int           `json:"failed_batches"`
	
	// 资源指标
	CHQueries        int           `json:"ch_queries"`
	ESQueries        int           `json:"es_queries"`
	ESWrites         int           `json:"es_writes"`
	
	// 错误统计
	Errors           []string      `json:"errors,omitempty"`
	
	mu sync.RWMutex
}

// NewSyncMetrics 创建同步指标
func NewSyncMetrics() *SyncMetrics {
	return &SyncMetrics{
		StartTime: time.Now(),
		MinBatchTime: time.Hour, // 初始化为一个大值
		Errors: make([]string, 0),
	}
}

// SetTotalClusters 设置总集群数
func (m *SyncMetrics) SetTotalClusters(count int) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.TotalClusters = count
}

// SetConcurrentWorkers 设置并发Worker数
func (m *SyncMetrics) SetConcurrentWorkers(workers int) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.ConcurrentWorkers = workers
}

// SetTotalBatches 设置总批次数
func (m *SyncMetrics) SetTotalBatches(batches int) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.TotalBatches = batches
}

// SetPreProcessTime 设置预处理时间
func (m *SyncMetrics) SetPreProcessTime(duration time.Duration) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.PreProcessTime = duration
}

// AddBatchResult 添加批次结果
func (m *SyncMetrics) AddBatchResult(newCount, updatedCount int, duration time.Duration, err error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.CompletedBatches++
	m.NewClusters += newCount
	m.UpdatedClusters += updatedCount
	m.ProcessedClusters += newCount + updatedCount
	
	// 更新批次时间统计
	if duration > m.MaxBatchTime {
		m.MaxBatchTime = duration
	}
	if duration < m.MinBatchTime {
		m.MinBatchTime = duration
	}
	
	// 计算平均批次时间
	if m.CompletedBatches > 0 {
		totalTime := m.ProcessingTime + duration
		m.AvgBatchTime = totalTime / time.Duration(m.CompletedBatches)
		m.ProcessingTime = totalTime
	}
	
	// 记录错误
	if err != nil {
		m.FailedBatches++
		m.Errors = append(m.Errors, err.Error())
	}
}

// AddError 添加错误
func (m *SyncMetrics) AddError(err error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.Errors = append(m.Errors, err.Error())
}

// IncrementCHQueries 增加ClickHouse查询次数
func (m *SyncMetrics) IncrementCHQueries() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.CHQueries++
}

// IncrementESQueries 增加ES查询次数
func (m *SyncMetrics) IncrementESQueries() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.ESQueries++
}

// IncrementESWrites 增加ES写入次数
func (m *SyncMetrics) IncrementESWrites() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.ESWrites++
}

// Finish 完成指标收集
func (m *SyncMetrics) Finish() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.EndTime = time.Now()
	m.TotalDuration = m.EndTime.Sub(m.StartTime)
	
	// 计算处理速度
	if m.TotalDuration.Seconds() > 0 {
		m.ClustersPerSecond = float64(m.ProcessedClusters) / m.TotalDuration.Seconds()
	}
	
	// 如果没有处理任何批次，重置最小批次时间
	if m.CompletedBatches == 0 {
		m.MinBatchTime = 0
	}
}

// GetSnapshot 获取指标快照
func (m *SyncMetrics) GetSnapshot() SyncMetrics {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	// 创建副本
	snapshot := *m
	snapshot.Errors = make([]string, len(m.Errors))
	copy(snapshot.Errors, m.Errors)
	
	return snapshot
}

// LogSummary 记录指标摘要
func (m *SyncMetrics) LogSummary(logger logger.Logger) {
	snapshot := m.GetSnapshot()
	
	logger.Infof("📊 同步性能指标摘要:")
	logger.Infof("  ├─ 集群统计: 总数=%d, 处理=%d, 新增=%d, 更新=%d, 失败=%d", 
		snapshot.TotalClusters, snapshot.ProcessedClusters, snapshot.NewClusters, snapshot.UpdatedClusters, snapshot.FailedClusters)
	logger.Infof("  ├─ 时间统计: 总耗时=%v, 预处理=%v, 处理=%v", 
		snapshot.TotalDuration, snapshot.PreProcessTime, snapshot.ProcessingTime)
	logger.Infof("  ├─ 性能统计: %.2f 集群/秒, 平均批次=%v, 最大批次=%v, 最小批次=%v", 
		snapshot.ClustersPerSecond, snapshot.AvgBatchTime, snapshot.MaxBatchTime, snapshot.MinBatchTime)
	logger.Infof("  ├─ 并发统计: Worker数=%d, 总批次=%d, 完成=%d, 失败=%d", 
		snapshot.ConcurrentWorkers, snapshot.TotalBatches, snapshot.CompletedBatches, snapshot.FailedBatches)
	logger.Infof("  ├─ 资源统计: CH查询=%d, ES查询=%d, ES写入=%d", 
		snapshot.CHQueries, snapshot.ESQueries, snapshot.ESWrites)
	
	if len(snapshot.Errors) > 0 {
		logger.Errorf("  └─ 错误统计: %d 个错误", len(snapshot.Errors))
		for i, err := range snapshot.Errors {
			if i < 5 { // 只显示前5个错误
				logger.Errorf("     %d. %s", i+1, err)
			} else {
				logger.Errorf("     ... 还有 %d 个错误", len(snapshot.Errors)-5)
				break
			}
		}
	} else {
		logger.Infof("  └─ 错误统计: 无错误 ✅")
	}
}

// MetricsCollector 指标收集器
type MetricsCollector struct {
	metrics *SyncMetrics
	logger  logger.Logger
	ctx     context.Context
	cancel  context.CancelFunc
}

// NewMetricsCollector 创建指标收集器
func NewMetricsCollector(logger logger.Logger) *MetricsCollector {
	ctx, cancel := context.WithCancel(context.Background())
	return &MetricsCollector{
		metrics: NewSyncMetrics(),
		logger:  logger,
		ctx:     ctx,
		cancel:  cancel,
	}
}

// GetMetrics 获取指标
func (mc *MetricsCollector) GetMetrics() *SyncMetrics {
	return mc.metrics
}

// StartProgressReporting 启动进度报告
func (mc *MetricsCollector) StartProgressReporting(interval time.Duration) {
	go func() {
		ticker := time.NewTicker(interval)
		defer ticker.Stop()
		
		for {
			select {
			case <-mc.ctx.Done():
				return
			case <-ticker.C:
				mc.reportProgress()
			}
		}
	}()
}

// reportProgress 报告进度
func (mc *MetricsCollector) reportProgress() {
	snapshot := mc.metrics.GetSnapshot()
	
	if snapshot.TotalBatches > 0 {
		progress := float64(snapshot.CompletedBatches) / float64(snapshot.TotalBatches) * 100
		elapsed := time.Since(snapshot.StartTime)
		
		var eta time.Duration
		if snapshot.CompletedBatches > 0 {
			avgTimePerBatch := elapsed / time.Duration(snapshot.CompletedBatches)
			remainingBatches := snapshot.TotalBatches - snapshot.CompletedBatches
			eta = avgTimePerBatch * time.Duration(remainingBatches)
		}
		
		mc.logger.Infof("📈 进度报告: %.1f%% (%d/%d批次), 已处理%d集群, 耗时%v, 预计剩余%v", 
			progress, snapshot.CompletedBatches, snapshot.TotalBatches, 
			snapshot.ProcessedClusters, elapsed, eta)
	}
}

// Stop 停止指标收集
func (mc *MetricsCollector) Stop() {
	mc.cancel()
	mc.metrics.Finish()
	mc.metrics.LogSummary(mc.logger)
}

// FormatDuration 格式化时间间隔
func FormatDuration(d time.Duration) string {
	if d < time.Second {
		return fmt.Sprintf("%.0fms", float64(d.Nanoseconds())/1e6)
	} else if d < time.Minute {
		return fmt.Sprintf("%.1fs", d.Seconds())
	} else if d < time.Hour {
		return fmt.Sprintf("%.1fm", d.Minutes())
	} else {
		return fmt.Sprintf("%.1fh", d.Hours())
	}
}
