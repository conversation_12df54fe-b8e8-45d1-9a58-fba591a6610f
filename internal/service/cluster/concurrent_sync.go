package cluster

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.woa.com/kmetis/cmdb/internal/model"
	"git.woa.com/kmetis/cmdb/internal/service/app"
	"golang.org/x/sync/errgroup"
	"golang.org/x/sync/semaphore"
)

// ConcurrentSyncService 并发同步服务
type ConcurrentSyncService struct {
	*SyncService
	concurrentWorkers int
	componentWorkers  int
	retryAttempts     int
	retryDelay        time.Duration
	metricsCollector  *MetricsCollector
}

// NewConcurrentSyncService 创建并发同步服务
func NewConcurrentSyncService(syncService *SyncService) (*ConcurrentSyncService, error) {
	retryDelay, err := time.ParseDuration(syncService.config.Sync.RetryDelay)
	if err != nil {
		retryDelay = 5 * time.Second
	}

	return &ConcurrentSyncService{
		SyncService:       syncService,
		concurrentWorkers: syncService.config.Sync.ConcurrentWorkers,
		componentWorkers:  syncService.config.Sync.ComponentWorkers,
		retryAttempts:     syncService.config.Sync.RetryAttempts,
		retryDelay:        retryDelay,
		metricsCollector:  NewMetricsCollector(syncService.logger),
	}, nil
}

// SyncByDateConcurrent 并发同步集群数据
func (cs *ConcurrentSyncService) SyncByDateConcurrent(ctx context.Context, date time.Time, dryRun bool) error {
	// 初始化指标收集
	metrics := cs.metricsCollector.GetMetrics()
	metrics.SetConcurrentWorkers(cs.concurrentWorkers)

	// 启动进度报告（每30秒报告一次）
	cs.metricsCollector.StartProgressReporting(30 * time.Second)
	defer cs.metricsCollector.Stop()

	startTime := time.Now()
	cs.logger.Infof("🚀 开始并发同步集群数据，日期: %s, 试运行: %v, 并发Worker数: %d",
		date.Format("2006-01-02"), dryRun, cs.concurrentWorkers)

	// 步骤1: 从ClickHouse获取集群基础数据
	chQueryDate := date.AddDate(0, 0, cs.config.Sync.CHQueryOffset)
	cs.logger.Infof("📊 步骤1: 从ClickHouse获取数据 - 同步日期=%s, 查询日期=%s (偏移%d天)",
		date.Format("2006-01-02"), chQueryDate.Format("2006-01-02"), cs.config.Sync.CHQueryOffset)

	clusters, err := cs.chClient.GetClustersByDate(ctx, chQueryDate)
	if err != nil {
		return fmt.Errorf("获取ClickHouse集群数据失败: %w", err)
	}

	if len(clusters) == 0 {
		cs.logger.Warn("ClickHouse中没有找到集群数据")
		return nil
	}

	cs.logger.Infof("📈 从ClickHouse获取到 %d 个集群", len(clusters))

	// 步骤2: 过滤有效集群
	validClusters := cs.filterValidClusters(clusters)
	cs.logger.Infof("✅ 过滤后有效集群数量: %d", len(validClusters))

	// 更新指标
	metrics.SetTotalClusters(len(validClusters))

	if len(validClusters) == 0 {
		cs.logger.Warn("没有有效的集群数据")
		return nil
	}

	// 步骤3: 预处理 - 批量获取基础数据
	preProcessStart := time.Now()
	preProcessedData, err := cs.preProcessData(ctx, date, chQueryDate, validClusters)
	if err != nil {
		return fmt.Errorf("预处理数据失败: %w", err)
	}
	preProcessDuration := time.Since(preProcessStart)
	cs.logger.Infof("⚡ 预处理完成，耗时: %v", preProcessDuration)

	// 更新预处理时间指标
	metrics.SetPreProcessTime(preProcessDuration)

	// 步骤4: 并发处理集群
	concurrentStart := time.Now()
	totalNew, totalUpdated, err := cs.processClustersInBatchesConcurrent(ctx, date, chQueryDate, validClusters, preProcessedData, dryRun)
	if err != nil {
		return fmt.Errorf("并发处理集群失败: %w", err)
	}
	concurrentDuration := time.Since(concurrentStart)

	totalDuration := time.Since(startTime)
	cs.logger.Infof("🎉 并发同步完成! 总耗时: %v (预处理: %v, 并发处理: %v)", 
		totalDuration, preProcessDuration, concurrentDuration)
	cs.logger.Infof("📊 处理结果: 总集群=%d, 新增=%d, 更新=%d", len(validClusters), totalNew, totalUpdated)
	cs.logger.Infof("⚡ 性能指标: %.2f 集群/秒", float64(len(validClusters))/totalDuration.Seconds())

	return nil
}

// PreProcessedData 预处理数据结构
type PreProcessedData struct {
	NodeCounts     map[string]uint64
	TKEPodCounts   map[string]model.PodCounts
	EKSPodCounts   map[string]model.PodCounts
	ReadESData     map[string]model.ESClusterDocument
	WriteESData    map[string]model.ESClusterDocument
	AppInfos       map[string]map[string]app.AppInfo
}

// preProcessData 预处理数据 - 批量获取所有需要的基础数据
func (cs *ConcurrentSyncService) preProcessData(ctx context.Context, date, chQueryDate time.Time, clusters []model.ClusterData) (*PreProcessedData, error) {
	cs.logger.Infof("🔄 开始预处理数据，集群数量: %d", len(clusters))

	// 按集群类型分组
	tkeClusterIds := make([]string, 0)
	eksClusterIds := make([]string, 0)
	allClusterIds := make([]string, 0, len(clusters))

	for _, cluster := range clusters {
		allClusterIds = append(allClusterIds, cluster.ClusterId)
		if cluster.IsTKE() {
			tkeClusterIds = append(tkeClusterIds, cluster.ClusterId)
		} else if cluster.IsEKS() {
			eksClusterIds = append(eksClusterIds, cluster.ClusterId)
		}
	}

	cs.logger.Infof("📊 集群分类: TKE=%d, EKS=%d", len(tkeClusterIds), len(eksClusterIds))

	// 使用errgroup并发获取各种数据
	g, gCtx := errgroup.WithContext(ctx)
	data := &PreProcessedData{
		NodeCounts:   make(map[string]uint64),
		TKEPodCounts: make(map[string]model.PodCounts),
		EKSPodCounts: make(map[string]model.PodCounts),
		ReadESData:   make(map[string]model.ESClusterDocument),
		WriteESData:  make(map[string]model.ESClusterDocument),
		AppInfos:     make(map[string]map[string]app.AppInfo),
	}

	// 1. 获取节点统计
	g.Go(func() error {
		cs.logger.Debugf("🔍 开始获取节点统计...")
		nodeCounts, err := cs.chClient.GetBatchNodeCounts(gCtx, chQueryDate, allClusterIds)
		if err != nil {
			cs.logger.Errorf("获取节点统计失败: %v", err)
			return err
		}
		data.NodeCounts = nodeCounts
		cs.logger.Infof("✅ 节点统计获取完成，集群数: %d", len(nodeCounts))
		return nil
	})

	// 2. 获取TKE Pod统计
	if len(tkeClusterIds) > 0 {
		g.Go(func() error {
			cs.logger.Debugf("🔍 开始获取TKE Pod统计...")
			tkePodCounts, err := cs.chClient.GetBatchPodCounts(gCtx, chQueryDate, tkeClusterIds, true)
			if err != nil {
				cs.logger.Errorf("获取TKE Pod统计失败: %v", err)
				return err
			}
			data.TKEPodCounts = tkePodCounts
			cs.logger.Infof("✅ TKE Pod统计获取完成，集群数: %d", len(tkePodCounts))
			return nil
		})
	}

	// 3. 获取EKS Pod统计
	if len(eksClusterIds) > 0 {
		g.Go(func() error {
			cs.logger.Debugf("🔍 开始获取EKS Pod统计...")
			eksPodCounts, err := cs.chClient.GetBatchPodCounts(gCtx, chQueryDate, eksClusterIds, false)
			if err != nil {
				cs.logger.Errorf("获取EKS Pod统计失败: %v", err)
				return err
			}
			data.EKSPodCounts = eksPodCounts
			cs.logger.Infof("✅ EKS Pod统计获取完成，集群数: %d", len(eksPodCounts))
			return nil
		})
	}

	// 4. 获取读取ES数据
	g.Go(func() error {
		cs.logger.Debugf("🔍 开始获取读取ES数据...")
		if err := cs.fetchBusinessInfoFromReadES(gCtx, date, tkeClusterIds, eksClusterIds, data.ReadESData); err != nil {
			cs.logger.Warnf("获取读取ES数据失败: %v", err)
			// 不返回错误，继续处理
		}
		cs.logger.Infof("✅ 读取ES数据获取完成，集群数: %d", len(data.ReadESData))
		return nil
	})

	// 5. 获取写入ES数据
	g.Go(func() error {
		cs.logger.Debugf("🔍 开始获取写入ES数据...")
		if err := cs.fetchTagsFromWriteES(gCtx, date, tkeClusterIds, eksClusterIds, data.WriteESData); err != nil {
			cs.logger.Warnf("获取写入ES数据失败: %v", err)
			// 不返回错误，继续处理
		}
		cs.logger.Infof("✅ 写入ES数据获取完成，集群数: %d", len(data.WriteESData))
		return nil
	})

	// 6. 获取App信息（仅TKE集群）
	if len(tkeClusterIds) > 0 {
		g.Go(func() error {
			cs.logger.Debugf("🔍 开始获取App信息...")
			appInfos, err := cs.appClient.GetBatchClusterApps(gCtx, tkeClusterIds)
			if err != nil {
				cs.logger.Warnf("获取App信息失败: %v", err)
				// 不返回错误，继续处理
			}
			data.AppInfos = appInfos
			cs.logger.Infof("✅ App信息获取完成，集群数: %d", len(appInfos))
			return nil
		})
	}

	// 等待所有预处理任务完成
	if err := g.Wait(); err != nil {
		return nil, fmt.Errorf("预处理数据失败: %w", err)
	}

	cs.logger.Infof("🎉 预处理数据完成")
	return data, nil
}

// BatchJob 批次任务
type BatchJob struct {
	BatchNum int
	Clusters []model.ClusterData
}

// BatchResult 批次结果
type BatchResult struct {
	BatchNum     int
	NewCount     int
	UpdatedCount int
	Error        error
	Duration     time.Duration
}

// processClustersInBatchesConcurrent 并发处理集群批次
func (cs *ConcurrentSyncService) processClustersInBatchesConcurrent(ctx context.Context, date, chQueryDate time.Time, clusters []model.ClusterData, preProcessedData *PreProcessedData, dryRun bool) (int, int, error) {
	batchSize := cs.config.Sync.BatchSize
	if batchSize <= 0 {
		batchSize = 1000
	}

	totalClusters := len(clusters)
	totalBatches := (totalClusters + batchSize - 1) / batchSize

	cs.logger.Infof("🔄 开始并发处理: 总集群数=%d, 批次大小=%d, 总批次数=%d, 并发Worker数=%d",
		totalClusters, batchSize, totalBatches, cs.concurrentWorkers)

	// 更新批次数量指标
	cs.metricsCollector.GetMetrics().SetTotalBatches(totalBatches)

	// 创建批次任务
	jobs := make([]BatchJob, 0, totalBatches)
	for i := 0; i < totalClusters; i += batchSize {
		end := i + batchSize
		if end > totalClusters {
			end = totalClusters
		}

		jobs = append(jobs, BatchJob{
			BatchNum: (i / batchSize) + 1,
			Clusters: clusters[i:end],
		})
	}

	// 使用semaphore控制并发数
	sem := semaphore.NewWeighted(int64(cs.concurrentWorkers))

	// 结果收集
	results := make(chan BatchResult, totalBatches)
	var wg sync.WaitGroup

	// 启动Worker处理批次
	for _, job := range jobs {
		wg.Add(1)
		go func(job BatchJob) {
			defer wg.Done()

			// 获取semaphore
			if err := sem.Acquire(ctx, 1); err != nil {
				results <- BatchResult{
					BatchNum: job.BatchNum,
					Error:    fmt.Errorf("获取semaphore失败: %w", err),
				}
				return
			}
			defer sem.Release(1)

			// 处理批次
			result := cs.processBatchWithRetry(ctx, date, chQueryDate, job, preProcessedData, dryRun)
			results <- result
		}(job)
	}

	// 等待所有Worker完成
	go func() {
		wg.Wait()
		close(results)
	}()

	// 收集结果
	var totalNew, totalUpdated int
	var errors []error
	processedBatches := 0

	for result := range results {
		processedBatches++

		// 更新指标
		cs.metricsCollector.GetMetrics().AddBatchResult(result.NewCount, result.UpdatedCount, result.Duration, result.Error)

		if result.Error != nil {
			cs.logger.Errorf("❌ 批次 %d 处理失败: %v", result.BatchNum, result.Error)
			errors = append(errors, fmt.Errorf("批次 %d: %w", result.BatchNum, result.Error))
		} else {
			totalNew += result.NewCount
			totalUpdated += result.UpdatedCount
			cs.logger.Infof("✅ 批次 %d/%d 完成: 新增=%d, 更新=%d, 耗时=%v",
				result.BatchNum, totalBatches, result.NewCount, result.UpdatedCount, result.Duration)
		}

		// 显示进度
		progress := float64(processedBatches) / float64(totalBatches) * 100
		cs.logger.Infof("📊 处理进度: %.1f%% (%d/%d)", progress, processedBatches, totalBatches)
	}

	// 检查是否有错误
	if len(errors) > 0 {
		cs.logger.Errorf("❌ %d 个批次处理失败", len(errors))
		// 返回第一个错误，但不中断整个流程
		return totalNew, totalUpdated, errors[0]
	}

	cs.logger.Infof("🎉 所有批次处理完成: 总新增=%d, 总更新=%d", totalNew, totalUpdated)
	return totalNew, totalUpdated, nil
}

// processBatchWithRetry 带重试的批次处理
func (cs *ConcurrentSyncService) processBatchWithRetry(ctx context.Context, date, chQueryDate time.Time, job BatchJob, preProcessedData *PreProcessedData, dryRun bool) BatchResult {
	startTime := time.Now()

	var lastErr error
	for attempt := 1; attempt <= cs.retryAttempts; attempt++ {
		if attempt > 1 {
			cs.logger.Infof("🔄 批次 %d 重试第 %d 次", job.BatchNum, attempt-1)
			time.Sleep(cs.retryDelay)
		}

		newCount, updatedCount, err := cs.processBatchConcurrent(ctx, date, chQueryDate, job.Clusters, preProcessedData, dryRun)
		if err == nil {
			return BatchResult{
				BatchNum:     job.BatchNum,
				NewCount:     newCount,
				UpdatedCount: updatedCount,
				Duration:     time.Since(startTime),
			}
		}

		lastErr = err
		cs.logger.Warnf("⚠️ 批次 %d 第 %d 次尝试失败: %v", job.BatchNum, attempt, err)
	}

	return BatchResult{
		BatchNum: job.BatchNum,
		Error:    fmt.Errorf("批次处理失败，已重试 %d 次: %w", cs.retryAttempts, lastErr),
		Duration: time.Since(startTime),
	}
}

// processBatchConcurrent 并发处理单个批次
func (cs *ConcurrentSyncService) processBatchConcurrent(ctx context.Context, date, chQueryDate time.Time, clusters []model.ClusterData, preProcessedData *PreProcessedData, dryRun bool) (int, int, error) {
	if len(clusters) == 0 {
		return 0, 0, nil
	}

	cs.logger.Debugf("🔄 开始处理批次，集群数量: %d", len(clusters))

	// 构建集群映射
	clusterMap := make(map[string]model.ClusterData)
	for _, cluster := range clusters {
		clusterMap[cluster.ClusterId] = cluster
	}

	// 步骤1: 并发收集组件版本信息
	componentVersionsMap, err := cs.collectComponentVersionsConcurrent(ctx, chQueryDate, clusters)
	if err != nil {
		cs.logger.Errorf("并发收集组件版本失败: %v", err)
		// 不返回错误，继续处理
		componentVersionsMap = make(map[string]map[string]model.ComponentVersion)
	}

	// 步骤2: 构建ES文档
	var documentsToUpsert []model.ESClusterDocument
	var newClusters []string
	var updatedClusters []string

	for _, cluster := range clusters {
		// 解析集群详细信息
		clusterDetail, err := cs.parseClusterDetail(cluster.Data)
		if err != nil {
			cs.logger.Errorf("解析集群 %s 详细信息失败: %v", cluster.ClusterId, err)
			continue
		}

		// 获取预处理的数据
		componentVersions := componentVersionsMap[cluster.ClusterId]
		if componentVersions == nil {
			componentVersions = make(map[string]model.ComponentVersion)
		}

		readData := preProcessedData.ReadESData[cluster.ClusterId]
		writeData := preProcessedData.WriteESData[cluster.ClusterId]
		nodeCount := preProcessedData.NodeCounts[cluster.ClusterId]
		appInfo := preProcessedData.AppInfos[cluster.ClusterId]

		// 获取Pod数量统计
		var podCounts model.PodCounts
		if cluster.IsTKE() {
			podCounts = preProcessedData.TKEPodCounts[cluster.ClusterId]
		} else {
			podCounts = preProcessedData.EKSPodCounts[cluster.ClusterId]
		}

		// 构建ES文档
		esDoc := cs.buildESDocument(cluster, clusterDetail, componentVersions, readData, writeData, date, appInfo, nodeCount, podCounts)
		documentsToUpsert = append(documentsToUpsert, esDoc)

		// 统计新增和更新
		if writeData.ClusterID != "" {
			updatedClusters = append(updatedClusters, cluster.ClusterId)
		} else {
			newClusters = append(newClusters, cluster.ClusterId)
		}
	}

	cs.logger.Debugf("📊 批次文档构建完成: 总数=%d, 新增=%d, 更新=%d",
		len(documentsToUpsert), len(newClusters), len(updatedClusters))

	// 步骤3: 批量写入ES
	if !dryRun && len(documentsToUpsert) > 0 {
		if err := cs.writeDocumentsByType(ctx, date, documentsToUpsert); err != nil {
			return 0, 0, fmt.Errorf("批量写入ES失败: %w", err)
		}
		cs.logger.Debugf("✅ 批次ES写入完成，文档数: %d", len(documentsToUpsert))
	} else if dryRun {
		cs.logger.Debugf("🔍 试运行模式，跳过ES写入，文档数: %d", len(documentsToUpsert))
	}

	return len(newClusters), len(updatedClusters), nil
}

// collectComponentVersionsConcurrent 并发收集组件版本信息
func (cs *ConcurrentSyncService) collectComponentVersionsConcurrent(ctx context.Context, date time.Time, clusters []model.ClusterData) (map[string]map[string]model.ComponentVersion, error) {
	if len(clusters) == 0 {
		return make(map[string]map[string]model.ComponentVersion), nil
	}

	cs.logger.Debugf("🔍 开始并发收集组件版本，集群数: %d, 组件Worker数: %d", len(clusters), cs.componentWorkers)

	// 使用semaphore控制组件收集的并发数
	sem := semaphore.NewWeighted(int64(cs.componentWorkers))

	// 结果映射
	resultMap := make(map[string]map[string]model.ComponentVersion)
	var mu sync.Mutex

	// 使用errgroup处理错误
	g, gCtx := errgroup.WithContext(ctx)

	for _, cluster := range clusters {
		cluster := cluster // 避免闭包问题
		g.Go(func() error {
			// 获取semaphore
			if err := sem.Acquire(gCtx, 1); err != nil {
				return err
			}
			defer sem.Release(1)

			// 收集单个集群的组件版本
			componentVersions, err := cs.collectComponentVersions(gCtx, date, cluster, nil)
			if err != nil {
				cs.logger.Warnf("收集集群 %s 组件版本失败: %v", cluster.ClusterId, err)
				// 不返回错误，继续处理其他集群
				componentVersions = make(map[string]model.ComponentVersion)
			}

			// 线程安全地更新结果
			mu.Lock()
			resultMap[cluster.ClusterId] = componentVersions
			mu.Unlock()

			return nil
		})
	}

	// 等待所有组件收集完成
	if err := g.Wait(); err != nil {
		return nil, fmt.Errorf("并发收集组件版本失败: %w", err)
	}

	cs.logger.Debugf("✅ 并发组件版本收集完成，成功收集: %d/%d", len(resultMap), len(clusters))
	return resultMap, nil
}
