package cluster

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.woa.com/kmetis/cmdb/internal/config"
	"git.woa.com/kmetis/cmdb/internal/model"
	"git.woa.com/kmetis/cmdb/internal/repository/clickhouse"
	"git.woa.com/kmetis/cmdb/internal/repository/elasticsearch"
	"git.woa.com/kmetis/cmdb/internal/repository/elasticsearch/ops"
	"git.woa.com/kmetis/cmdb/internal/service/app"
	"git.woa.com/kmetis/cmdb/pkg/logger"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
)

// SyncService 集群同步服务
type SyncService struct {
	chClient         *clickhouse.Client
	esClient         *elasticsearch.Client
	esReader         *ops.Reader
	appClient        *app.Client
	componentManager *ComponentManager
	config           *config.Config
	logger           logger.Logger
}

// NewSyncService 创建同步服务
func NewSyncService(cfg *config.Config, logger logger.Logger) (*SyncService, error) {
	// 创建ClickHouse客户端
	chClient, err := clickhouse.NewClient(cfg.ClickHouse, logger)
	if err != nil {
		return nil, fmt.Errorf("创建ClickHouse客户端失败: %w", err)
	}

	// 创建Elasticsearch客户端
	esClient, err := elasticsearch.NewClient(cfg.Elasticsearch, logger)
	if err != nil {
		return nil, fmt.Errorf("创建Elasticsearch客户端失败: %w", err)
	}

	// 创建ES读取器
	esReader := ops.NewReader(esClient.GetReadClient(), logger)

	// 创建App信息客户端
	appClient, err := app.NewClient(cfg.MySQL, logger)
	if err != nil {
		return nil, fmt.Errorf("创建App信息客户端失败: %w", err)
	}

	// 创建组件管理器
	componentManager, err := NewComponentManager(cfg, logger)
	if err != nil {
		return nil, fmt.Errorf("创建组件管理器失败: %w", err)
	}

	return &SyncService{
		chClient:         chClient,
		esClient:         esClient,
		esReader:         esReader,
		appClient:        appClient,
		componentManager: componentManager,
		config:           cfg,
		logger:           logger,
	}, nil
}

// SyncByDate 按日期同步集群数据
func (s *SyncService) SyncByDate(ctx context.Context, date time.Time, dryRun bool) error {
	s.logger.Infof("开始同步集群数据，日期: %s, 试运行: %v", date.Format("2006-01-02"), dryRun)

	// 步骤1: 从ClickHouse获取集群基础数据
	chQueryDate := date.AddDate(0, 0, s.config.Sync.CHQueryOffset)
	s.logger.Infof("步骤1: 从ClickHouse获取数据 - 同步日期=%s, 查询日期=%s (偏移%d天)",
		date.Format("2006-01-02"), chQueryDate.Format("2006-01-02"), s.config.Sync.CHQueryOffset)

	clusters, err := s.chClient.GetClustersByDate(ctx, chQueryDate)
	if err != nil {
		return fmt.Errorf("获取ClickHouse集群数据失败: %w", err)
	}

	if len(clusters) == 0 {
		s.logger.Warn("ClickHouse中没有找到集群数据")
		return nil
	}

	s.logger.Infof("从ClickHouse获取到 %d 个集群", len(clusters))

	// 步骤2: 过滤有效集群（只处理4/8类型的集群）
	validClusters := s.filterValidClusters(clusters)
	s.logger.Infof("过滤后有效集群数量: %d", len(validClusters))

	if len(validClusters) == 0 {
		s.logger.Warn("没有有效的集群数据")
		return nil
	}

	// 步骤3: 分批处理集群，避免内存爆炸
	return s.processClustersInBatches(ctx, date, chQueryDate, validClusters, dryRun)
}

// filterValidClusters 过滤有效集群
func (s *SyncService) filterValidClusters(clusters []model.ClusterData) []model.ClusterData {
	var validClusters []model.ClusterData

	for _, cluster := range clusters {
		// EKS集群直接通过ClusterType判断，不需要检查4/8类型
		if cluster.ClusterType == "eks" {
			validClusters = append(validClusters, cluster)
			s.logger.Debugf("EKS集群 %s 通过过滤", cluster.ClusterId)
		} else {
			// TKE集群需要检查4/8类型
			clusterType := cluster.GetClusterTypeFromData()
			if clusterType == model.ClusterTypeManaged || clusterType == model.ClusterTypeIndependent {
				validClusters = append(validClusters, cluster)
				s.logger.Debugf("TKE集群 %s 通过过滤，类型: %s", cluster.ClusterId, clusterType)
			} else {
				s.logger.Debugf("跳过TKE集群 %s，类型不支持: %s", cluster.ClusterId, clusterType)
			}
		}
	}

	return validClusters
}

// processClustersInBatches 分批处理集群，避免内存爆炸
func (s *SyncService) processClustersInBatches(ctx context.Context, date, chQueryDate time.Time, clusters []model.ClusterData, dryRun bool) error {
	batchSize := s.config.Sync.BatchSize
	if batchSize <= 0 {
		batchSize = 1000 // 默认批次大小
	}

	totalClusters := len(clusters)
	totalBatches := (totalClusters + batchSize - 1) / batchSize

	s.logger.Infof("开始分批处理: 总集群数=%d, 批次大小=%d, 总批次数=%d", totalClusters, batchSize, totalBatches)
	s.logger.Infof("注意: 如果是第一次运行，将自动创建ES索引，且不会有历史标签数据")

	var totalNewClusters, totalUpdatedClusters int

	for i := 0; i < totalClusters; i += batchSize {
		end := i + batchSize
		if end > totalClusters {
			end = totalClusters
		}

		batch := clusters[i:end]
		batchNum := (i / batchSize) + 1

		s.logger.Infof("处理批次 %d/%d: 集群数量=%d", batchNum, totalBatches, len(batch))

		newCount, updatedCount, err := s.processBatch(ctx, date, chQueryDate, batch, dryRun)
		if err != nil {
			s.logger.Errorf("处理批次 %d 失败: %v", batchNum, err)
			return fmt.Errorf("处理批次 %d 失败: %w", batchNum, err)
		}

		totalNewClusters += newCount
		totalUpdatedClusters += updatedCount

		s.logger.Infof("批次 %d 完成: 新增=%d, 更新=%d", batchNum, newCount, updatedCount)
	}

	s.logger.Infof("所有批次处理完成: 总新增=%d, 总更新=%d", totalNewClusters, totalUpdatedClusters)
	return nil
}

// processBatch 处理单个批次的集群
func (s *SyncService) processBatch(ctx context.Context, date, chQueryDate time.Time, clusters []model.ClusterData, dryRun bool) (int, int, error) {
	if len(clusters) == 0 {
		return 0, 0, nil
	}

	// 按集群类型分组
	tkeClusterIds := make([]string, 0)
	eksClusterIds := make([]string, 0)
	clusterMap := make(map[string]model.ClusterData)

	for _, cluster := range clusters {
		clusterMap[cluster.ClusterId] = cluster
		if cluster.IsTKE() {
			tkeClusterIds = append(tkeClusterIds, cluster.ClusterId)
		} else if cluster.IsEKS() {
			eksClusterIds = append(eksClusterIds, cluster.ClusterId)
		}
	}

	s.logger.Infof("批次集群分类: TKE=%d, EKS=%d", len(tkeClusterIds), len(eksClusterIds))

	// 步骤2: 从读取ES获取业务信息
	readESData := make(map[string]model.ESClusterDocument)
	if err := s.fetchBusinessInfoFromReadES(ctx, date, tkeClusterIds, eksClusterIds, readESData); err != nil {
		s.logger.Warnf("从读取ES获取业务信息失败: %v", err)
		// 不返回错误，继续处理
	}

	// 步骤3: 从写入ES获取动态标签（前一天的数据）
	writeESData := make(map[string]model.ESClusterDocument)
	if err := s.fetchTagsFromWriteES(ctx, date, tkeClusterIds, eksClusterIds, writeESData); err != nil {
		s.logger.Warnf("从写入ES获取动态标签失败: %v", err)
		// 不返回错误，继续处理
	}

	// 步骤4: 批量获取资源统计
	allClusterIds := make([]string, 0, len(clusters))
	for _, cluster := range clusters {
		allClusterIds = append(allClusterIds, cluster.ClusterId)
	}

	// 4.1: 批量获取节点数量统计
	nodeCounts := make(map[string]uint64)
	if len(allClusterIds) > 0 {
		result, err := s.chClient.GetBatchNodeCounts(ctx, chQueryDate, allClusterIds)
		if err != nil {
			s.logger.Errorf("批量获取节点数量统计失败: %v", err)
			// 设置默认值
			for _, clusterID := range allClusterIds {
				nodeCounts[clusterID] = 0
			}
		} else {
			nodeCounts = result
		}
	}

	// 4.2: 批量获取TKE集群Pod数量统计
	tkePodCounts := make(map[string]model.PodCounts)
	if len(tkeClusterIds) > 0 {
		result, err := s.chClient.GetBatchPodCounts(ctx, chQueryDate, tkeClusterIds, true)
		if err != nil {
			s.logger.Errorf("批量获取TKE Pod数量统计失败: %v", err)
			// 设置默认值
			for _, clusterID := range tkeClusterIds {
				tkePodCounts[clusterID] = model.PodCounts{TotalPodCount: 0, EkletPodCount: 0}
			}
		} else {
			tkePodCounts = result
		}
	}

	// 4.3: 批量获取EKS集群Pod数量统计
	eksPodCounts := make(map[string]model.PodCounts)
	if len(eksClusterIds) > 0 {
		result, err := s.chClient.GetBatchPodCounts(ctx, chQueryDate, eksClusterIds, false)
		if err != nil {
			s.logger.Errorf("批量获取EKS Pod数量统计失败: %v", err)
			// 设置默认值
			for _, clusterID := range eksClusterIds {
				eksPodCounts[clusterID] = model.PodCounts{TotalPodCount: 0, EkletPodCount: 0}
			}
		} else {
			eksPodCounts = result
		}
	}

	// 步骤5: 批量获取App信息（仅TKE集群）
	appInfos := make(map[string]map[string]app.AppInfo)
	if len(tkeClusterIds) > 0 {
		var err error
		appInfos, err = s.appClient.GetBatchClusterApps(ctx, tkeClusterIds)
		if err != nil {
			s.logger.Errorf("批量获取App信息失败: %v", err)
			// 设置默认值
			for _, clusterID := range tkeClusterIds {
				appInfos[clusterID] = make(map[string]app.AppInfo)
			}
		}
	}

	// 步骤6: 处理每个集群
	var documentsToUpsert []model.ESClusterDocument
	var newClusters []string
	var updatedClusters []string

	for _, cluster := range clusters {
		// 解析集群详细信息
		clusterDetail, err := s.parseClusterDetail(cluster.Data)
		if err != nil {
			s.logger.Errorf("解析集群 %s 详细信息失败: %v", cluster.ClusterId, err)
			continue
		}

		// 收集组件版本信息
		componentVersions, err := s.collectComponentVersions(ctx, chQueryDate, cluster, clusterDetail)
		if err != nil {
			s.logger.Errorf("收集集群 %s 组件版本失败: %v", cluster.ClusterId, err)
			// 继续处理，不因为组件版本收集失败而中断
		}

		// 获取现有数据
		readData := readESData[cluster.ClusterId]
		writeData := writeESData[cluster.ClusterId]

		// 构建ES文档
		clusterAppInfo := appInfos[cluster.ClusterId]
		nodeCount := nodeCounts[cluster.ClusterId]

		// 获取Pod数量统计
		var podCounts model.PodCounts
		if cluster.IsTKE() {
			podCounts = tkePodCounts[cluster.ClusterId]
		} else {
			// EKS集群的Pod数量统计 - 从pod表查询
			podCounts = eksPodCounts[cluster.ClusterId]
		}

		esDoc := s.buildESDocument(cluster, clusterDetail, componentVersions, readData, writeData, date, clusterAppInfo, nodeCount, podCounts)

		documentsToUpsert = append(documentsToUpsert, esDoc)

		// 统计新增和更新
		if writeData.ClusterID != "" {
			updatedClusters = append(updatedClusters, cluster.ClusterId)
		} else {
			newClusters = append(newClusters, cluster.ClusterId)
		}
	}

	s.logger.Infof("批次处理结果: 新增=%d, 更新=%d", len(newClusters), len(updatedClusters))

	if dryRun {
		s.logger.Info("试运行模式，不实际写入数据")
		return len(newClusters), len(updatedClusters), nil
	}

	// 步骤5: 按集群类型分别写入ES
	if err := s.writeDocumentsByType(ctx, date, documentsToUpsert); err != nil {
		return 0, 0, fmt.Errorf("写入ES失败: %w", err)
	}

	return len(newClusters), len(updatedClusters), nil
}

// fetchBusinessInfoFromReadES 从读取ES获取业务信息
func (s *SyncService) fetchBusinessInfoFromReadES(ctx context.Context, date time.Time, tkeClusterIds, eksClusterIds []string, readESData map[string]model.ESClusterDocument) error {
	// 调试配置值
	s.logger.Debugf("配置检查: ESQueryOffset=%d, CHQueryOffset=%d", s.config.Sync.ESQueryOffset, s.config.Sync.CHQueryOffset)

	// 计算读取ES查询日期
	readESQueryDate := date.AddDate(0, 0, s.config.Sync.ESQueryOffset)

	s.logger.Infof("步骤2: 从读取ES获取业务信息 - 同步日期=%s, 查询日期=%s (偏移%d天)",
		date.Format("2006-01-02"), readESQueryDate.Format("2006-01-02"), s.config.Sync.ESQueryOffset)

	// 查询TKE集群的业务信息
	if len(tkeClusterIds) > 0 {
		tkeReadData, err := s.esReader.GetBusinessInfoFromReadES(ctx, readESQueryDate, tkeClusterIds, "tke")
		if err != nil {
			s.logger.Warnf("从读取ES获取TKE集群业务信息失败: %v", err)
		} else {
			for id, doc := range tkeReadData {
				readESData[id] = doc
			}
			s.logger.Infof("成功从读取ES获取 %d 个TKE集群的业务信息", len(tkeReadData))
		}
	}

	// 查询EKS集群的业务信息
	if len(eksClusterIds) > 0 {
		eksReadData, err := s.esReader.GetBusinessInfoFromReadES(ctx, readESQueryDate, eksClusterIds, "eks")
		if err != nil {
			s.logger.Warnf("从读取ES获取EKS集群业务信息失败: %v", err)
		} else {
			for id, doc := range eksReadData {
				readESData[id] = doc
			}
			s.logger.Infof("成功从读取ES获取 %d 个EKS集群的业务信息", len(eksReadData))
		}
	}

	return nil
}

// fetchTagsFromWriteES 从写入ES获取动态标签
func (s *SyncService) fetchTagsFromWriteES(ctx context.Context, date time.Time, tkeClusterIds, eksClusterIds []string, writeESData map[string]model.ESClusterDocument) error {
	// 计算写入ES查询日期（固定前一天）
	writeESQueryDate := date.AddDate(0, 0, -1)

	s.logger.Infof("步骤3: 从写入ES获取动态标签 - 同步日期=%s, 查询日期=%s (固定前一天)",
		date.Format("2006-01-02"), writeESQueryDate.Format("2006-01-02"))

	// 查询TKE集群的动态标签
	if len(tkeClusterIds) > 0 {
		tkeWriteData, err := s.esReader.GetTagsFromWriteES(ctx, s.esClient.GetWriteClient(), writeESQueryDate, tkeClusterIds, "tke")
		if err != nil {
			s.logger.Warnf("从写入ES获取TKE集群标签失败: %v", err)
		} else {
			for id, doc := range tkeWriteData {
				writeESData[id] = doc
			}
			s.logger.Infof("成功从写入ES获取 %d 个TKE集群的历史标签", len(tkeWriteData))
		}
	}

	// 查询EKS集群的动态标签
	if len(eksClusterIds) > 0 {
		eksWriteData, err := s.esReader.GetTagsFromWriteES(ctx, s.esClient.GetWriteClient(), writeESQueryDate, eksClusterIds, "eks")
		if err != nil {
			s.logger.Warnf("从写入ES获取EKS集群标签失败: %v", err)
		} else {
			for id, doc := range eksWriteData {
				writeESData[id] = doc
			}
			s.logger.Infof("成功从写入ES获取 %d 个EKS集群的历史标签", len(eksWriteData))
		}
	}

	return nil
}

// parseClusterDetail 解析集群详细信息
func (s *SyncService) parseClusterDetail(data string) (*model.ClusterDetail, error) {
	var detail model.ClusterDetail
	if err := json.Unmarshal([]byte(data), &detail); err != nil {
		return nil, fmt.Errorf("解析集群详细信息失败: %w", err)
	}

	// 保存原始数据，用于EKS集群解析annotations等信息
	detail.RawData = []byte(data)

	// 对于EKS集群，需要从rawData中解析MetaClusterID
	if detail.MetaClusterID == "" {
		extractedMetaID := s.extractMetaClusterID(&detail)
		if extractedMetaID != "" {
			detail.MetaClusterID = extractedMetaID
			s.logger.Infof("EKS集群 %s 解析到MetaClusterID: %s", detail.Name, extractedMetaID)
		} else {
			s.logger.Warnf("EKS集群 %s 未能解析到MetaClusterID", detail.Name)
		}
	}

	return &detail, nil
}

// collectComponentVersions 收集组件版本信息（优化版本，减少ClickHouse查询）
func (s *SyncService) collectComponentVersions(ctx context.Context, date time.Time, cluster model.ClusterData, detail *model.ClusterDetail) (map[string]model.ComponentVersion, error) {
	componentVersions := make(map[string]model.ComponentVersion)

	// 判断集群类型
	isManaged := cluster.IsManaged()

	s.logger.Infof("开始收集集群 %s 组件版本 - 平台: %s, 类型: %s, 是否托管: %v, MetaClusterID: %s",
		cluster.ClusterId, cluster.ClusterType, cluster.GetClusterTypeFromData(), isManaged, detail.MetaClusterID)

	// 分组组件：按查询来源分组，减少ClickHouse查询次数
	metaComponents := make([]string, 0)
	userComponents := make([]string, 0)

	for _, componentName := range s.componentManager.GetComponentNames() {
		shouldQueryFromMeta := s.componentManager.ShouldQueryFromMetaCluster(componentName, isManaged, cluster.ClusterType)
		s.logger.Debugf("组件 %s 查询来源判断: shouldQueryFromMeta=%v, isManaged=%v, clusterType=%s",
			componentName, shouldQueryFromMeta, isManaged, cluster.ClusterType)

		if shouldQueryFromMeta {
			metaComponents = append(metaComponents, componentName)
		} else {
			userComponents = append(userComponents, componentName)
		}
	}

	s.logger.Debugf("组件分组 - Meta集群组件: %v, 用户集群组件: %v", metaComponents, userComponents)

	// 批量查询Meta集群组件
	if len(metaComponents) > 0 {
		metaWorkloadNames := make([]string, 0)
		for _, componentName := range metaComponents {
			workloadName := s.componentManager.GetWorkloadName(componentName, cluster.ClusterId, isManaged, cluster.ClusterType)
			metaWorkloadNames = append(metaWorkloadNames, workloadName)
		}

		s.logger.Infof("批量查询Meta集群workloads - MetaClusterID: %s, ClusterID: %s, Workloads: %v", detail.MetaClusterID, cluster.ClusterId, metaWorkloadNames)
		metaWorkloads, err := s.chClient.GetMetaWorkloads(ctx, date, []string{detail.MetaClusterID}, []string{cluster.ClusterId}, metaWorkloadNames)
		if err != nil {
			s.logger.Errorf("批量获取Meta集群workloads失败: %v", err)
		} else {
			s.processWorkloads(metaWorkloads, metaComponents, componentVersions)
		}
	}

	// 批量查询用户集群组件
	if len(userComponents) > 0 {
		// 分离控制面组件和普通组件
		controlPlaneComponents := make([]string, 0)
		regularComponents := make([]string, 0)

		for _, componentName := range userComponents {
			if s.isControlPlaneComponent(componentName) && !isManaged {
				// 独立集群的控制面组件需要从pod表查询
				controlPlaneComponents = append(controlPlaneComponents, componentName)
			} else {
				// 普通组件从workload表查询
				regularComponents = append(regularComponents, componentName)
			}
		}

		// 查询普通组件（从workload表）
		if len(regularComponents) > 0 {
			userWorkloadNames := make([]string, 0)
			for _, componentName := range regularComponents {
				workloadName := s.componentManager.GetWorkloadName(componentName, cluster.ClusterId, isManaged, cluster.ClusterType)
				userWorkloadNames = append(userWorkloadNames, workloadName)
			}

			s.logger.Debugf("批量查询用户集群workloads: %v", userWorkloadNames)
			userWorkloads, err := s.chClient.GetUserWorkloads(ctx, date, []string{cluster.ClusterId}, userWorkloadNames)
			if err != nil {
				s.logger.Errorf("批量获取用户集群workloads失败: %v", err)
			} else {
				s.processWorkloads(userWorkloads, regularComponents, componentVersions)
			}
		}

		// 查询独立集群控制面组件（从pod表）
		if len(controlPlaneComponents) > 0 {
			s.logger.Infof("批量查询独立集群静态Pod - ClusterID: %s, 组件: %v", cluster.ClusterId, controlPlaneComponents)
			staticPods, err := s.chClient.GetStaticPods(ctx, date, []string{cluster.ClusterId}, controlPlaneComponents)
			if err != nil {
				s.logger.Errorf("批量获取独立集群静态Pod失败: %v", err)
			} else {
				s.processStaticPods(staticPods, controlPlaneComponents, componentVersions)
			}
		}
	}

	s.logger.Infof("集群 %s 组件版本收集完成，共收集到 %d 个组件", cluster.ClusterId, len(componentVersions))
	return componentVersions, nil
}

// processStaticPods 处理静态Pod数据
func (s *SyncService) processStaticPods(pods []model.PodData, componentNames []string, componentVersions map[string]model.ComponentVersion) {
	s.logger.Debugf("开始处理 %d 个静态Pod", len(pods))

	// 为每个组件创建一个映射，用于去重（每个组件只取一个Pod实例）
	componentPodMap := make(map[string]model.PodData)

	for _, pod := range pods {
		// 根据Pod名称确定组件类型
		var componentName string
		for _, name := range componentNames {
			if strings.HasPrefix(pod.Name, name+"-") {
				componentName = name
				break
			}
		}

		if componentName == "" {
			s.logger.Warnf("无法识别静态Pod %s 的组件类型", pod.Name)
			continue
		}

		// 每个组件只保留一个Pod实例（用于获取镜像和参数信息）
		if _, exists := componentPodMap[componentName]; !exists {
			componentPodMap[componentName] = pod
			s.logger.Debugf("为组件 %s 选择Pod实例: %s", componentName, pod.Name)
		}
	}

	// 解析每个组件的Pod数据
	for componentName, pod := range componentPodMap {
		containerNames := s.componentManager.GetContainerNames(componentName)
		if len(containerNames) == 0 {
			s.logger.Warnf("组件 %s 没有配置容器名称", componentName)
			continue
		}

		s.logger.Debugf("解析静态Pod组件: %s, 容器名称: %v", componentName, containerNames)

		version, err := s.parseStaticPodVersion(pod, containerNames, componentName)
		if err != nil {
			s.logger.Errorf("解析静态Pod组件 %s 版本失败: %v", componentName, err)
			continue
		}

		componentVersions[componentName] = version
		s.logger.Infof("成功解析静态Pod组件 %s: %s:%s", componentName, version.Image, version.Tag)
	}
}

// parseStaticPodVersion 解析静态Pod组件版本信息
func (s *SyncService) parseStaticPodVersion(pod model.PodData, containerNames []string, componentName string) (model.ComponentVersion, error) {
	// 解析Pod数据
	var podWorkload model.PodWorkload
	if err := json.Unmarshal([]byte(pod.Data), &podWorkload.Pod); err != nil {
		return model.ComponentVersion{}, fmt.Errorf("解析Pod数据失败: %w", err)
	}

	// 使用统一的版本提取逻辑
	podTemplate := podWorkload.GetPodTemplate()
	return s.extractVersionFromPodTemplate(podTemplate, containerNames, "pod", componentName, true)
}

// buildESDocument 构建ES文档
func (s *SyncService) buildESDocument(cluster model.ClusterData, detail *model.ClusterDetail, componentVersions map[string]model.ComponentVersion, readESDoc, writeESDoc model.ESClusterDocument, date time.Time, appInfo map[string]app.AppInfo, nodeCount uint64, podCounts model.PodCounts) model.ESClusterDocument {
	now := time.Now()

	// 从ClickHouse数据构建基础文档
	doc := model.ESClusterDocument{
		// 基础信息 - 来自ClickHouse
		ClusterID:     cluster.ClusterId,
		ClusterName:   cluster.Name,
		ClusterType:   cluster.ClusterType,
		ClusterStatus: cluster.Status,
		Region:        cluster.Region,
		AppID:         fmt.Sprintf("%d", cluster.AppId),
		K8sVersion:    cluster.Version, // 使用cluster.Version作为K8s版本

		// 创建者信息 - 从ClickHouse Context解析
		Uin:           s.extractUin(detail),
		SubAccountUin: s.extractSubAccountUin(detail),

		// 集群创建时间
		ClusterCreatedAt: s.extractClusterCreatedAt(detail),

		// EKS集群的产品名称
		ProductName: s.extractProductName(detail),

		// 网络信息 - 来自ClickHouse
		ClusterLevel:  detail.ClusterLevel,
		VpcID:         detail.UniqVpcID,
		ClusterCIDR:   detail.ClusterCIDR,
		ServiceCIDR:   detail.ServiceCIDR,
		MetaClusterID: detail.MetaClusterID,

		// 组件版本信息 - 新收集的数据（字段名修改）
		ComponentVersion: componentVersions,

		// 时间信息
		Date:      date.Format("2006-01-02"),
		UpdatedAt: now,
	}

	// 从读取ES获取业务信息（账户信息和CPU/内存）
	if readESDoc.ClusterID != "" {
		doc.AccountName = readESDoc.AccountName
		doc.AccountType = readESDoc.AccountType
		doc.AccountLevel = readESDoc.AccountLevel
		doc.IsBigUser = readESDoc.IsBigUser
		doc.CPUCount = readESDoc.CPUCount
		doc.MemCount = readESDoc.MemCount
		s.logger.Debugf("集群 %s 从读取ES获取了业务信息", cluster.ClusterId)
	} else {
		// 读取ES中没有数据，设置默认值
		doc.AccountName = ""
		doc.AccountType = ""
		doc.AccountLevel = ""
		doc.IsBigUser = 0
		doc.CPUCount = 0
		doc.MemCount = 0
		s.logger.Debugf("集群 %s 在读取ES中不存在，使用默认业务信息", cluster.ClusterId)
	}

	// 设置节点数量（从ClickHouse node表查询）
	doc.NodeCount = int(nodeCount)
	s.logger.Debugf("集群 %s 节点数量: %d", cluster.ClusterId, nodeCount)

	// 设置Pod数量统计
	doc.PodCount = int(podCounts.TotalPodCount)
	doc.EksPodCount = int(podCounts.EkletPodCount)

	if cluster.IsTKE() {
		s.logger.Debugf("集群 %s TKE Pod统计: 总数=%d, eklet数=%d",
			cluster.ClusterId, podCounts.TotalPodCount, podCounts.EkletPodCount)
	} else {
		s.logger.Debugf("集群 %s EKS Pod统计: 总数=%d, 超级节点数=%d",
			cluster.ClusterId, podCounts.TotalPodCount, podCounts.EkletPodCount)
	}

	// 设置TKE集群管理类型
	if cluster.IsTKE() {
		managementType := s.extractClusterManagementType(cluster)
		doc.ClusterManagementType = managementType
		s.logger.Debugf("集群 %s TKE管理类型: %s", cluster.ClusterId, managementType)
	}

	// 设置App信息（仅TKE集群）
	if cluster.IsTKE() && len(appInfo) > 0 {
		doc.AddonInfo = make(map[string]interface{})
		for appName, info := range appInfo {
			doc.AddonInfo[appName] = info
		}
		s.logger.Debugf("集群 %s 设置了 %d 个App信息", cluster.ClusterId, len(appInfo))
	}

	// 从写入ES保留动态标签
	if writeESDoc.ClusterID != "" {
		doc.Tags = writeESDoc.Tags
		if doc.Tags == nil {
			doc.Tags = make(map[string]interface{})
		}
		s.logger.Debugf("集群 %s 从写入ES保留了 %d 个动态标签", cluster.ClusterId, len(doc.Tags))
		doc.CreatedAt = writeESDoc.CreatedAt
	} else {
		doc.Tags = make(map[string]interface{})
		doc.CreatedAt = now
		s.logger.Debugf("集群 %s 是新集群，初始化空标签", cluster.ClusterId)
	}

	// 初始化CMDB元数据
	if doc.CmdbMd == nil {
		doc.CmdbMd = make(map[string]interface{})
	}

	return doc
}

// writeDocumentsByType 按集群类型分别写入ES
func (s *SyncService) writeDocumentsByType(ctx context.Context, date time.Time, documents []model.ESClusterDocument) error {
	if len(documents) == 0 {
		return nil
	}

	// 按集群类型分组
	tkeDocuments := make([]model.ESClusterDocument, 0)
	eksDocuments := make([]model.ESClusterDocument, 0)

	for _, doc := range documents {
		switch doc.ClusterType {
		case "tke":
			tkeDocuments = append(tkeDocuments, doc)
		case "eks":
			eksDocuments = append(eksDocuments, doc)
		default:
			s.logger.Warnf("未知集群类型: %s, 集群ID: %s", doc.ClusterType, doc.ClusterID)
		}
	}

	// 写入TKE集群
	if len(tkeDocuments) > 0 {
		if err := s.esClient.BulkUpsertByType(ctx, date, tkeDocuments, "tke"); err != nil {
			return fmt.Errorf("写入TKE集群失败: %w", err)
		}
		s.logger.Infof("成功写入 %d 个TKE集群到ES", len(tkeDocuments))
	}

	// 写入EKS集群
	if len(eksDocuments) > 0 {
		if err := s.esClient.BulkUpsertByType(ctx, date, eksDocuments, "eks"); err != nil {
			return fmt.Errorf("写入EKS集群失败: %w", err)
		}
		s.logger.Infof("成功写入 %d 个EKS集群到ES", len(eksDocuments))
	}

	return nil
}

// extractUin 从集群详细信息中提取Uin
func (s *SyncService) extractUin(detail *model.ClusterDetail) string {
	// 首先尝试从EKS集群的rawData中获取annotations
	if rawDataBytes, ok := detail.RawData.([]byte); ok {
		var rawData map[string]interface{}
		if err := json.Unmarshal(rawDataBytes, &rawData); err == nil {
			if metadata, ok := rawData["metadata"].(map[string]interface{}); ok {
				if annotations, ok := metadata["annotations"].(map[string]interface{}); ok {
					// owneruin是主账号uin
					if ownerUin, exists := annotations["eks.tke.cloud.tencent.com/owner-uin"]; exists {
						if uinStr, ok := ownerUin.(string); ok {
							return uinStr
						}
					}
				}
			}
		}
	}

	// 然后尝试从detail.Annotations中获取（如果有的话）
	if detail.Annotations != nil {
		// owneruin是主账号uin
		if ownerUin, exists := detail.Annotations["eks.tke.cloud.tencent.com/owner-uin"]; exists {
			return ownerUin
		}
	}

	// 然后尝试从Context中获取（TKE集群）
	if detail.Context == "" {
		return ""
	}

	// 解析Context JSON
	var contextData map[string]interface{}
	if err := json.Unmarshal([]byte(detail.Context), &contextData); err != nil {
		s.logger.Debugf("解析Context JSON失败: %v", err)
		return ""
	}

	// 从Context.CreateCtx中解析Uin
	if createCtx, exists := contextData["CreateCtx"]; exists {
		if createCtxMap, ok := createCtx.(map[string]interface{}); ok {
			if uin, exists := createCtxMap["Uin"]; exists {
				if uinStr, ok := uin.(string); ok {
					return uinStr
				}
				// 如果是数字类型，转换为字符串
				if uinFloat, ok := uin.(float64); ok {
					return strconv.FormatInt(int64(uinFloat), 10)
				}
			}
		}
	}

	return ""
}

// extractSubAccountUin 从集群详细信息中提取SubAccountUin
func (s *SyncService) extractSubAccountUin(detail *model.ClusterDetail) string {
	// 首先尝试从EKS集群的rawData中获取annotations
	if rawDataBytes, ok := detail.RawData.([]byte); ok {
		var rawData map[string]interface{}
		if err := json.Unmarshal(rawDataBytes, &rawData); err == nil {
			if metadata, ok := rawData["metadata"].(map[string]interface{}); ok {
				if annotations, ok := metadata["annotations"].(map[string]interface{}); ok {
					// creatoruin是子账号uin
					if creatorUin, exists := annotations["eks.tke.cloud.tencent.com/creator-uin"]; exists {
						if uinStr, ok := creatorUin.(string); ok {
							return uinStr
						}
					}
				}
			}
		}
	}

	// 然后尝试从detail.Annotations中获取（如果有的话）
	if detail.Annotations != nil {
		// creatoruin是子账号uin
		if creatorUin, exists := detail.Annotations["eks.tke.cloud.tencent.com/creator-uin"]; exists {
			return creatorUin
		}
	}

	// 然后尝试从Context中获取（TKE集群）
	if detail.Context == "" {
		return ""
	}

	// 解析Context JSON
	var contextData map[string]interface{}
	if err := json.Unmarshal([]byte(detail.Context), &contextData); err != nil {
		s.logger.Debugf("解析Context JSON失败: %v", err)
		return ""
	}

	// 从Context.CreateCtx中解析SubAccountUin
	if createCtx, exists := contextData["CreateCtx"]; exists {
		if createCtxMap, ok := createCtx.(map[string]interface{}); ok {
			if subUin, exists := createCtxMap["SubAccountUin"]; exists {
				if subUinStr, ok := subUin.(string); ok {
					return subUinStr
				}
				// 如果是数字类型，转换为字符串
				if subUinFloat, ok := subUin.(float64); ok {
					return strconv.FormatInt(int64(subUinFloat), 10)
				}
			}
		}
	}

	return ""
}

// extractMetaClusterID 从集群详细信息中提取MetaClusterID
func (s *SyncService) extractMetaClusterID(detail *model.ClusterDetail) string {
	// 首先尝试从EKS集群的rawData中获取annotations
	if rawDataBytes, ok := detail.RawData.([]byte); ok {
		var rawData map[string]interface{}
		if err := json.Unmarshal(rawDataBytes, &rawData); err == nil {
			if metadata, ok := rawData["metadata"].(map[string]interface{}); ok {
				if annotations, ok := metadata["annotations"].(map[string]interface{}); ok {
					// EKS集群的MetaClusterID在annotations中
					if metaCluster, exists := annotations["eks.tke.cloud.tencent.com/metacluster"]; exists {
						if metaClusterStr, ok := metaCluster.(string); ok {
							s.logger.Debugf("从EKS annotations中解析到MetaClusterID: %s", metaClusterStr)
							return metaClusterStr
						}
					}
				}
			}
		}
	}

	// 然后尝试从detail.Annotations中获取（如果有的话）
	if detail.Annotations != nil {
		if metaCluster, exists := detail.Annotations["eks.tke.cloud.tencent.com/metacluster"]; exists {
			s.logger.Debugf("从detail.Annotations中解析到MetaClusterID: %s", metaCluster)
			return metaCluster
		}
	}

	// 如果都没有找到，返回原有的MetaClusterID
	return detail.MetaClusterID
}

// extractClusterCreatedAt 从集群详细信息中提取创建时间
func (s *SyncService) extractClusterCreatedAt(detail *model.ClusterDetail) time.Time {
	// 首先尝试从EKS集群的rawData中获取creationTimestamp
	if rawDataBytes, ok := detail.RawData.([]byte); ok {
		var rawData map[string]interface{}
		if err := json.Unmarshal(rawDataBytes, &rawData); err == nil {
			if metadata, ok := rawData["metadata"].(map[string]interface{}); ok {
				if creationTimestamp, exists := metadata["creationTimestamp"]; exists {
					if timestampStr, ok := creationTimestamp.(string); ok {
						if t, err := time.Parse(time.RFC3339, timestampStr); err == nil {
							return t
						}
					}
				}
			}
		}
	}

	// 然后尝试从annotations中获取（EKS集群）
	if detail.Annotations != nil {
		if createdAt, exists := detail.Annotations["eks.tke.cloud.tencent.com/created-at"]; exists {
			if t, err := time.Parse(time.RFC3339, createdAt); err == nil {
				return t
			}
		}
	}

	// 然后尝试从Context中获取（TKE集群）
	if detail.Context == "" {
		return time.Time{}
	}

	// 解析Context JSON
	var contextData map[string]interface{}
	if err := json.Unmarshal([]byte(detail.Context), &contextData); err != nil {
		s.logger.Debugf("解析Context JSON失败: %v", err)
		return time.Time{}
	}

	// 从Context.CreateCtx中解析创建时间
	if createCtx, exists := contextData["CreateCtx"]; exists {
		if createCtxMap, ok := createCtx.(map[string]interface{}); ok {
			if createdAt, exists := createCtxMap["CreatedAt"]; exists {
				if createdAtStr, ok := createdAt.(string); ok {
					if t, err := time.Parse(time.RFC3339, createdAtStr); err == nil {
						return t
					}
					// 尝试其他时间格式
					if t, err := time.Parse("2006-01-02 15:04:05", createdAtStr); err == nil {
						return t
					}
				}
			}
		}
	}

	// 如果都没有找到，尝试从detail的其他字段获取
	if detail.CreatedAt != "" {
		if t, err := time.Parse(time.RFC3339, detail.CreatedAt); err == nil {
			return t
		}
		if t, err := time.Parse("2006-01-02 15:04:05", detail.CreatedAt); err == nil {
			return t
		}
	}

	return time.Time{}
}

// extractProductName 从集群详细信息中提取ProductName
func (s *SyncService) extractProductName(detail *model.ClusterDetail) string {
	// 首先尝试从EKS集群的rawData中获取annotations
	if rawDataBytes, ok := detail.RawData.([]byte); ok {
		var rawData map[string]interface{}
		if err := json.Unmarshal(rawDataBytes, &rawData); err == nil {
			if metadata, ok := rawData["metadata"].(map[string]interface{}); ok {
				if annotations, ok := metadata["annotations"].(map[string]interface{}); ok {
					// EKS集群的ProductName在annotations中
					if productName, exists := annotations["eks.tke.cloud.tencent.com/product-name"]; exists {
						if productNameStr, ok := productName.(string); ok {
							s.logger.Debugf("从EKS annotations中解析到ProductName: %s", productNameStr)
							return productNameStr
						}
					}
				}
			}
		}
	}

	// 然后尝试从detail.Annotations中获取（如果有的话）
	if detail.Annotations != nil {
		if productName, exists := detail.Annotations["eks.tke.cloud.tencent.com/product-name"]; exists {
			s.logger.Debugf("从detail.Annotations中解析到ProductName: %s", productName)
			return productName
		}
	}

	return ""
}

// parseComponentVersion 解析组件版本信息
func (s *SyncService) parseComponentVersion(workload model.WorkloadData, containerNames []string, componentName string) (model.ComponentVersion, error) {
	// 根据workload类型解析不同的结构
	switch strings.ToLower(workload.WorkloadType) {
	case "deployment":
		var deployment appsv1.Deployment
		if err := json.Unmarshal([]byte(workload.Data), &deployment); err != nil {
			return model.ComponentVersion{}, fmt.Errorf("解析Deployment数据失败: %w", err)
		}
		return s.extractVersionFromPodTemplate(deployment.Spec.Template, containerNames, workload.WorkloadType, componentName, false)

	case "daemonset":
		var daemonset appsv1.DaemonSet
		if err := json.Unmarshal([]byte(workload.Data), &daemonset); err != nil {
			return model.ComponentVersion{}, fmt.Errorf("解析DaemonSet数据失败: %w", err)
		}
		return s.extractVersionFromPodTemplate(daemonset.Spec.Template, containerNames, workload.WorkloadType, componentName, false)

	default:
		return model.ComponentVersion{}, fmt.Errorf("不支持的workload类型: %s", workload.WorkloadType)
	}
}

// extractVersionFromPodTemplate 从PodTemplate中提取版本信息
func (s *SyncService) extractVersionFromPodTemplate(podTemplate corev1.PodTemplateSpec, containerNames []string, workloadType string, componentName string, isStaticPod bool) (model.ComponentVersion, error) {
	// 查找匹配的容器
	for _, containerName := range containerNames {
		for _, container := range podTemplate.Spec.Containers {
			if container.Name == containerName {
				// 解析镜像信息
				imageParts := strings.Split(container.Image, ":")
				if len(imageParts) >= 2 {
					image := strings.Join(imageParts[:len(imageParts)-1], ":")
					tag := imageParts[len(imageParts)-1]

					// 获取参数：根据集群类型和组件类型选择参数来源
					var args []string
					isControlPlaneComponent := s.isControlPlaneComponent(componentName)

					if isControlPlaneComponent {
						if isStaticPod {
							// 独立集群静态Pod：参数在args字段中
							if len(container.Args) > 0 {
								args = container.Args
							} else if len(container.Command) > 0 {
								args = container.Command
							}
						} else {
							// 托管集群控制面：参数在command字段中
							if len(container.Command) > 0 {
								args = container.Command
							} else if len(container.Args) > 0 {
								args = container.Args
							}
						}
					} else {
						// 其他组件优先使用Args
						if len(container.Args) > 0 {
							args = container.Args
						} else if len(container.Command) > 0 {
							args = container.Command
						}
					}

					// 获取环境变量
					var env []corev1.EnvVar
					if container.Env != nil {
						env = container.Env
					}

					version := model.ComponentVersion{
						Name:   container.Name,
						Image:  image,
						Tag:    tag,
						Args:   args,
						Env:    env,
						Type:   workloadType,
						Source: "workload",
					}

					s.logger.Debugf("解析到组件版本: %s -> %s:%s", container.Name, image, tag)
					return version, nil
				}
			}
		}
	}

	return model.ComponentVersion{}, fmt.Errorf("未找到匹配的容器")
}

// isControlPlaneComponent 判断是否为控制面组件
func (s *SyncService) isControlPlaneComponent(componentName string) bool {
	return s.componentManager.IsControlPlaneComponent(componentName)
}

// processWorkloads 处理workload数据，提取组件版本信息
func (s *SyncService) processWorkloads(workloads []model.WorkloadData, componentNames []string, componentVersions map[string]model.ComponentVersion) {
	// 为每个组件查找对应的workload
	for _, componentName := range componentNames {
		found := false

		for _, workload := range workloads {
			// 检查workload名称是否匹配组件
			if s.isWorkloadMatchComponent(workload.WorkloadName, componentName) {
				containerNames := s.componentManager.GetContainerNames(componentName)
				version, err := s.parseComponentVersion(workload, containerNames, componentName)
				if err != nil {
					s.logger.Errorf("解析组件 %s 版本失败: %v", componentName, err)
					continue
				}

				if version.Tag != "" {
					componentVersions[componentName] = version
					s.logger.Infof("收集到组件 %s 版本: %s (镜像: %s)", componentName, version.Tag, version.Image)
					found = true
					break
				}
			}
		}

		if !found {
			s.logger.Debugf("组件 %s 没有找到匹配的workload数据", componentName)
		}
	}
}

// isWorkloadMatchComponent 判断workload是否匹配组件
func (s *SyncService) isWorkloadMatchComponent(workloadName, componentName string) bool {
	// 精确匹配
	if workloadName == componentName {
		return true
	}

	// 包含匹配
	if strings.Contains(workloadName, componentName) {
		return true
	}

	// 去掉kube-前缀匹配
	if strings.HasPrefix(componentName, "kube-") {
		shortName := strings.Replace(componentName, "kube-", "", 1)
		if strings.Contains(workloadName, shortName) {
			return true
		}
	}

	// 特殊匹配规则
	switch componentName {
	case "kube-controller-manager":
		return strings.Contains(workloadName, "controller-manager")
	case "kube-apiserver":
		return strings.Contains(workloadName, "apiserver")
	case "kube-scheduler":
		return strings.Contains(workloadName, "scheduler")
	}

	return false
}

// extractClusterManagementType 提取TKE集群管理类型
func (s *SyncService) extractClusterManagementType(cluster model.ClusterData) string {
	if !cluster.IsTKE() {
		return "" // 非TKE集群不设置此字段
	}

	// 解析Data字段中的ClusterType
	var data struct {
		ClusterType int `json:"ClusterType"`
	}

	if err := json.Unmarshal([]byte(cluster.Data), &data); err != nil {
		s.logger.Warnf("解析集群 %s 的Data字段失败: %v", cluster.ClusterId, err)
		return "unknown"
	}

	// 根据ClusterType判断管理类型
	switch data.ClusterType {
	case 4:
		return "independent" // 独立集群
	case 8:
		return "managed" // 托管集群
	default:
		s.logger.Warnf("集群 %s 未知的ClusterType: %d", cluster.ClusterId, data.ClusterType)
		return "unknown"
	}
}
