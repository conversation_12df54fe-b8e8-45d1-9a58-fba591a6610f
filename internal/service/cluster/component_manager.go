package cluster

import (
	"fmt"
	"strings"

	"git.woa.com/kmetis/cmdb/internal/config"
	"git.woa.com/kmetis/cmdb/pkg/logger"
)

// ComponentManager 组件管理器
type ComponentManager struct {
	components map[string]config.ComponentConfig
	global     config.GlobalComponentConfig
	logger     logger.Logger
}

// NewComponentManager 创建组件管理器
func NewComponentManager(cfg *config.Config, logger logger.Logger) (*ComponentManager, error) {
	// 设置默认值
	global := cfg.GlobalComponents
	if global.DefaultQuerySource == "" {
		global.DefaultQuerySource = "user"
	}
	if global.ComponentCollectionTimeout == 0 {
		global.ComponentCollectionTimeout = 30
	}

	manager := &ComponentManager{
		components: cfg.Components,
		global:     global,
		logger:     logger,
	}

	logger.Infof("组件管理器初始化完成，加载了 %d 个组件配置", len(cfg.Components))

	return manager, nil
}

// GetComponentNames 获取所有组件名称
func (cm *ComponentManager) GetComponentNames() []string {
	var names []string
	for name := range cm.components {
		names = append(names, name)
	}
	return names
}

// GetContainerNames 获取组件的容器名称列表
func (cm *ComponentManager) GetContainerNames(componentName string) []string {
	component, exists := cm.components[componentName]
	if !exists {
		cm.logger.Warnf("未找到组件配置: %s", componentName)
		return []string{componentName} // 返回组件名作为默认容器名
	}

	return component.ContainerNames
}

// IsControlPlaneComponent 判断是否为控制面组件
func (cm *ComponentManager) IsControlPlaneComponent(componentName string) bool {
	component, exists := cm.components[componentName]
	if !exists {
		return false
	}

	return component.ComponentType == "control-plane"
}

// GetWorkloadName 获取组件的workload名称
func (cm *ComponentManager) GetWorkloadName(componentName, clusterID string, isManaged bool, clusterType string) string {
	component, exists := cm.components[componentName]
	if !exists {
		cm.logger.Warnf("未找到组件配置: %s", componentName)
		return componentName
	}

	var workloadNameTemplate string

	if clusterType == "eks" {
		// EKS集群
		workloadNameTemplate = component.Platforms.EKS.WorkloadName
	} else {
		// TKE集群
		switch component.ComponentType {
		case "user", "special":
			// 用户组件和特殊组件使用统一配置
			workloadNameTemplate = component.Platforms.TKE.WorkloadName
		default:
			// 控制面组件按托管/独立区分
			if isManaged && component.Platforms.TKE.Managed != nil {
				workloadNameTemplate = component.Platforms.TKE.Managed.WorkloadName
			} else if !isManaged && component.Platforms.TKE.Independent != nil {
				workloadNameTemplate = component.Platforms.TKE.Independent.WorkloadName
			}
		}
	}

	if workloadNameTemplate == "" {
		cm.logger.Warnf("组件 %s 未找到workload名称模板，使用组件名", componentName)
		return componentName
	}

	// 替换占位符
	workloadName := strings.ReplaceAll(workloadNameTemplate, "{clusterID}", clusterID)
	workloadName = strings.ReplaceAll(workloadName, "{componentName}", componentName)

	cm.logger.Debugf("组件 %s 最终workload名称: %s", componentName, workloadName)

	return workloadName
}

// ShouldQueryFromMetaCluster 判断组件是否应该从Meta集群查询
func (cm *ComponentManager) ShouldQueryFromMetaCluster(componentName string, isManaged bool, clusterType string) bool {
	component, exists := cm.components[componentName]
	if !exists {
		cm.logger.Warnf("未找到组件配置: %s，使用默认查询来源", componentName)
		return cm.global.DefaultQuerySource == "meta"
	}

	var querySource string

	if clusterType == "eks" {
		// EKS集群
		querySource = component.Platforms.EKS.QuerySource
	} else {
		// TKE集群
		switch component.ComponentType {
		case "user":
			// 用户组件永远在用户集群
			querySource = component.Platforms.TKE.QuerySource
		case "special":
			// 特殊组件（如eklet）
			querySource = component.Platforms.TKE.QuerySource
		default:
			// 控制面组件按托管/独立区分
			if isManaged && component.Platforms.TKE.Managed != nil {
				querySource = component.Platforms.TKE.Managed.QuerySource
			} else if !isManaged && component.Platforms.TKE.Independent != nil {
				querySource = component.Platforms.TKE.Independent.QuerySource
			}
		}
	}

	if querySource == "" {
		cm.logger.Warnf("组件 %s 未找到查询来源配置，使用默认值", componentName)
		querySource = cm.global.DefaultQuerySource
	}

	result := querySource == "meta"
	cm.logger.Debugf("组件 %s 最终查询来源: querySource=%s, shouldQueryFromMeta=%v", componentName, querySource, result)
	return result
}

// IsComponentCollectionEnabled 检查是否启用组件收集
func (cm *ComponentManager) IsComponentCollectionEnabled() bool {
	return cm.global.EnableComponentCollection
}

// GetComponentCollectionTimeout 获取组件收集超时时间
func (cm *ComponentManager) GetComponentCollectionTimeout() int {
	return cm.global.ComponentCollectionTimeout
}

// GetComponentDescription 获取组件描述
func (cm *ComponentManager) GetComponentDescription(componentName string) string {
	component, exists := cm.components[componentName]
	if !exists {
		return ""
	}
	return component.Description
}

// ValidateComponent 验证组件配置（新逻辑）
func (cm *ComponentManager) ValidateComponent(componentName string) error {
	component, exists := cm.components[componentName]
	if !exists {
		return fmt.Errorf("组件 %s 不存在", componentName)
	}

	// 检查必要字段
	if len(component.ContainerNames) == 0 {
		return fmt.Errorf("组件 %s 缺少容器名称配置", componentName)
	}

	// 检查平台配置
	if component.Platforms.TKE.WorkloadName == "" &&
		(component.Platforms.TKE.Managed == nil || component.Platforms.TKE.Managed.WorkloadName == "") {
		return fmt.Errorf("组件 %s 缺少TKE workload名称配置", componentName)
	}

	if component.Platforms.EKS.WorkloadName == "" {
		return fmt.Errorf("组件 %s 缺少EKS workload名称配置", componentName)
	}

	return nil
}

// ListComponents 列出所有组件及其配置信息（新逻辑）
func (cm *ComponentManager) ListComponents() map[string]interface{} {
	result := make(map[string]interface{})

	for name, component := range cm.components {
		result[name] = map[string]interface{}{
			"description":     component.Description,
			"container_names": component.ContainerNames,
			"component_type":  component.ComponentType,
			"platforms":       component.Platforms,
		}
	}

	return result
}
