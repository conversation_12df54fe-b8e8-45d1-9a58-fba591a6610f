package cluster_test

import (
	"testing"
)

// TestComponentQueryLogic 测试组件查询逻辑
func TestComponentQueryLogic(t *testing.T) {

	// 测试用例
	testCases := []struct {
		componentName string
		isManaged     bool
		expected      bool
		description   string
	}{
		// 托管集群测试
		{"kube-apiserver", true, true, "托管集群的kube-apiserver应该从Meta集群查询"},
		{"kube-scheduler", true, true, "托管集群的kube-scheduler应该从Meta集群查询"},
		{"kube-controller-manager", true, true, "托管集群的kube-controller-manager应该从Meta集群查询"},
		{"eklet", true, true, "托管集群的eklet应该从Meta集群查询"},
		{"coredns", true, false, "托管集群的coredns应该从用户集群查询"},
		{"kube-proxy", true, false, "托管集群的kube-proxy应该从用户集群查询"},

		// 独立集群测试
		{"kube-apiserver", false, false, "独立集群的kube-apiserver应该从用户集群查询"},
		{"coredns", false, false, "独立集群的coredns应该从用户集群查询"},
		{"kube-proxy", false, false, "独立集群的kube-proxy应该从用户集群查询"},
		{"eklet", false, false, "独立集群的eklet应该从用户集群查询"},
	}

	for _, tc := range testCases {
		t.Run(tc.description, func(t *testing.T) {
			result := shouldQueryFromMetaCluster(tc.componentName, tc.isManaged)
			if result != tc.expected {
				t.Errorf("组件 %s (托管=%v): 期望 %v, 实际 %v",
					tc.componentName, tc.isManaged, tc.expected, result)
			} else {
				t.Logf("✅ %s", tc.description)
			}
		})
	}
}

// shouldQueryFromMetaCluster 复制的逻辑函数用于测试
func shouldQueryFromMetaCluster(componentName string, isManaged bool) bool {
	// 独立集群的所有组件都在用户集群
	if !isManaged {
		return false
	}

	// 托管集群中，只有控制面组件在Meta集群
	switch componentName {
	case "kube-apiserver", "kube-scheduler", "kube-controller-manager":
		return true // 控制面组件在Meta集群
	case "coredns", "kube-proxy":
		return false // 用户组件在用户集群
	case "eklet":
		return true // eklet总是在Meta集群（托管集群）
	default:
		return false
	}
}
