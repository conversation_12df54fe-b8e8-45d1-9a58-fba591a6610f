package cluster

import (
	"context"
	"fmt"
	"testing"
	"time"

	"git.woa.com/kmetis/cmdb/internal/config"
	"git.woa.com/kmetis/cmdb/internal/model"
	"git.woa.com/kmetis/cmdb/internal/service/app"
	"git.woa.com/kmetis/cmdb/pkg/logger"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestConcurrentSyncPerformance 测试并发同步性能
func TestConcurrentSyncPerformance(t *testing.T) {
	// 跳过集成测试，除非明确指定
	if testing.Short() {
		t.Skip("跳过性能测试")
	}

	// 创建测试配置
	cfg := &config.Config{
		Sync: config.SyncConfig{
			BatchSize:         1000,
			ConcurrentWorkers: 4,
			ComponentWorkers:  2,
			RetryAttempts:     2,
			RetryDelay:        "1s",
		},
	}

	// 创建测试logger
	testLogger := logger.NewTestLogger()

	// 创建模拟的SyncService
	syncService := &SyncService{
		config: cfg,
		logger: testLogger,
	}

	// 创建并发同步服务
	concurrentService, err := NewConcurrentSyncService(syncService)
	require.NoError(t, err)

	// 测试指标收集器
	assert.NotNil(t, concurrentService.metricsCollector)
	assert.Equal(t, 4, concurrentService.concurrentWorkers)
	assert.Equal(t, 2, concurrentService.componentWorkers)
}

// TestMetricsCollection 测试指标收集
func TestMetricsCollection(t *testing.T) {
	testLogger := logger.NewTestLogger()
	collector := NewMetricsCollector(testLogger)
	
	metrics := collector.GetMetrics()
	
	// 测试基础指标设置
	metrics.SetTotalClusters(1000)
	metrics.SetConcurrentWorkers(8)
	metrics.SetTotalBatches(10)
	
	assert.Equal(t, 1000, metrics.TotalClusters)
	assert.Equal(t, 8, metrics.ConcurrentWorkers)
	assert.Equal(t, 10, metrics.TotalBatches)
	
	// 测试批次结果添加
	metrics.AddBatchResult(50, 30, 5*time.Second, nil)
	metrics.AddBatchResult(40, 35, 4*time.Second, nil)
	
	assert.Equal(t, 2, metrics.CompletedBatches)
	assert.Equal(t, 90, metrics.NewClusters)
	assert.Equal(t, 65, metrics.UpdatedClusters)
	assert.Equal(t, 155, metrics.ProcessedClusters)
	assert.Equal(t, 5*time.Second, metrics.MaxBatchTime)
	assert.Equal(t, 4*time.Second, metrics.MinBatchTime)
	
	// 测试完成指标
	metrics.Finish()
	assert.True(t, metrics.ClustersPerSecond > 0)
	
	collector.Stop()
}

// TestBatchJobCreation 测试批次任务创建
func TestBatchJobCreation(t *testing.T) {
	// 创建测试集群数据
	clusters := make([]model.ClusterData, 2500)
	for i := 0; i < 2500; i++ {
		clusters[i] = model.ClusterData{
			ClusterId:   fmt.Sprintf("cluster-%d", i),
			Name:        fmt.Sprintf("test-cluster-%d", i),
			ClusterType: "tke",
		}
	}
	
	batchSize := 1000
	totalClusters := len(clusters)
	totalBatches := (totalClusters + batchSize - 1) / batchSize
	
	// 创建批次任务
	jobs := make([]BatchJob, 0, totalBatches)
	for i := 0; i < totalClusters; i += batchSize {
		end := i + batchSize
		if end > totalClusters {
			end = totalClusters
		}
		
		jobs = append(jobs, BatchJob{
			BatchNum: (i / batchSize) + 1,
			Clusters: clusters[i:end],
		})
	}
	
	// 验证批次创建
	assert.Equal(t, 3, len(jobs)) // 2500 / 1000 = 3 批次
	assert.Equal(t, 1, jobs[0].BatchNum)
	assert.Equal(t, 1000, len(jobs[0].Clusters))
	assert.Equal(t, 2, jobs[1].BatchNum)
	assert.Equal(t, 1000, len(jobs[1].Clusters))
	assert.Equal(t, 3, jobs[2].BatchNum)
	assert.Equal(t, 500, len(jobs[2].Clusters)) // 最后一批次
}

// TestPreProcessedDataStructure 测试预处理数据结构
func TestPreProcessedDataStructure(t *testing.T) {
	data := &PreProcessedData{
		NodeCounts:   make(map[string]uint64),
		TKEPodCounts: make(map[string]model.PodCounts),
		EKSPodCounts: make(map[string]model.PodCounts),
		ReadESData:   make(map[string]model.ESClusterDocument),
		WriteESData:  make(map[string]model.ESClusterDocument),
		AppInfos:     make(map[string]map[string]app.AppInfo),
	}
	
	// 添加测试数据
	data.NodeCounts["cluster-1"] = 10
	data.TKEPodCounts["cluster-1"] = model.PodCounts{
		TotalPodCount: 100,
		EkletPodCount: 20,
	}
	data.ReadESData["cluster-1"] = model.ESClusterDocument{
		ClusterID: "cluster-1",
		AccountName: "test-account",
	}
	
	// 验证数据结构
	assert.Equal(t, uint64(10), data.NodeCounts["cluster-1"])
	assert.Equal(t, uint64(100), data.TKEPodCounts["cluster-1"].TotalPodCount)
	assert.Equal(t, "cluster-1", data.ReadESData["cluster-1"].ClusterID)
}

// BenchmarkConcurrentProcessing 并发处理基准测试
func BenchmarkConcurrentProcessing(b *testing.B) {
	// 创建测试配置
	cfg := &config.Config{
		Sync: config.SyncConfig{
			BatchSize:         500,
			ConcurrentWorkers: 4,
			ComponentWorkers:  2,
			RetryAttempts:     1,
			RetryDelay:        "100ms",
		},
	}
	
	testLogger := logger.NewTestLogger()
	syncService := &SyncService{
		config: cfg,
		logger: testLogger,
	}
	
	concurrentService, err := NewConcurrentSyncService(syncService)
	require.NoError(b, err)
	
	// 创建测试集群数据
	clusters := make([]model.ClusterData, 1000)
	for i := 0; i < 1000; i++ {
		clusters[i] = model.ClusterData{
			ClusterId:   fmt.Sprintf("bench-cluster-%d", i),
			Name:        fmt.Sprintf("benchmark-cluster-%d", i),
			ClusterType: "tke",
			Data:        `{"ClusterLevel": "L5", "UniqVpcID": "vpc-test"}`,
		}
	}
	
	// 创建预处理数据
	preProcessedData := &PreProcessedData{
		NodeCounts:   make(map[string]uint64),
		TKEPodCounts: make(map[string]model.PodCounts),
		EKSPodCounts: make(map[string]model.PodCounts),
		ReadESData:   make(map[string]model.ESClusterDocument),
		WriteESData:  make(map[string]model.ESClusterDocument),
		AppInfos:     make(map[string]map[string]app.AppInfo),
	}
	
	// 填充测试数据
	for _, cluster := range clusters {
		preProcessedData.NodeCounts[cluster.ClusterId] = 5
		preProcessedData.TKEPodCounts[cluster.ClusterId] = model.PodCounts{
			TotalPodCount: 50,
			EkletPodCount: 10,
		}
	}
	
	b.ResetTimer()
	
	// 基准测试
	for i := 0; i < b.N; i++ {
		ctx := context.Background()
		date := time.Now()
		
		// 注意：这里只测试批次创建和任务分发的性能
		// 实际的数据库操作在基准测试中会被跳过
		_, _, err := concurrentService.processClustersInBatchesConcurrent(
			ctx, date, date, clusters, preProcessedData, true, // dryRun=true
		)
		
		if err != nil {
			b.Errorf("基准测试失败: %v", err)
		}
	}
}

// TestErrorHandling 测试错误处理
func TestErrorHandling(t *testing.T) {
	testLogger := logger.NewTestLogger()
	collector := NewMetricsCollector(testLogger)
	metrics := collector.GetMetrics()
	
	// 测试错误添加
	testErr := fmt.Errorf("测试错误")
	metrics.AddError(testErr)
	
	snapshot := metrics.GetSnapshot()
	assert.Equal(t, 1, len(snapshot.Errors))
	assert.Equal(t, "测试错误", snapshot.Errors[0])
	
	// 测试批次错误
	metrics.AddBatchResult(0, 0, time.Second, testErr)
	assert.Equal(t, 1, metrics.FailedBatches)
	
	collector.Stop()
}

// TestConfigurationValidation 测试配置验证
func TestConfigurationValidation(t *testing.T) {
	tests := []struct {
		name   string
		config config.SyncConfig
		valid  bool
	}{
		{
			name: "有效配置",
			config: config.SyncConfig{
				BatchSize:         1000,
				ConcurrentWorkers: 8,
				ComponentWorkers:  4,
				RetryAttempts:     3,
				RetryDelay:        "5s",
			},
			valid: true,
		},
		{
			name: "零并发Worker",
			config: config.SyncConfig{
				BatchSize:         1000,
				ConcurrentWorkers: 0,
				ComponentWorkers:  4,
				RetryAttempts:     3,
				RetryDelay:        "5s",
			},
			valid: false,
		},
		{
			name: "无效重试延迟",
			config: config.SyncConfig{
				BatchSize:         1000,
				ConcurrentWorkers: 8,
				ComponentWorkers:  4,
				RetryAttempts:     3,
				RetryDelay:        "invalid",
			},
			valid: false,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cfg := &config.Config{Sync: tt.config}
			testLogger := logger.NewTestLogger()
			syncService := &SyncService{config: cfg, logger: testLogger}
			
			_, err := NewConcurrentSyncService(syncService)
			
			if tt.valid {
				assert.NoError(t, err)
			} else {
				// 注意：当前实现可能不会在创建时验证所有配置
				// 这个测试主要是为了确保未来的配置验证逻辑
				t.Logf("配置: %+v, 错误: %v", tt.config, err)
			}
		})
	}
}
