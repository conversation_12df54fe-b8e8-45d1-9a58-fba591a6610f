package model

import (
	"encoding/json"
	"fmt"
	"time"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
)

// ClusterData ClickHouse中的集群数据结构
type ClusterData struct {
	Region      string    `json:"Region" ch:"Region"`
	AppId       uint64    `json:"AppId" ch:"AppId"`
	ClusterId   string    `json:"ClusterId" ch:"ClusterId"`
	Name        string    `json:"Name" ch:"Name"`
	ClusterType string    `json:"ClusterType" ch:"ClusterType"`
	Version     string    `json:"Version" ch:"Version"`
	Status      string    `json:"Status" ch:"Status"`
	Data        string    `json:"Data" ch:"Data"`
	DateDay     time.Time `json:"DateDay" ch:"DateDay"`
}

// ClusterDetail 集群详细信息（从Data字段解析）
type ClusterDetail struct {
	ID                       int64             `json:"ID"`
	AppId                    uint64            `json:"AppId"`
	ClusterInstanceId        string            `json:"ClusterInstanceId"`
	Name                     string            `json:"Name"`
	Description              string            `json:"Description"`
	LifeState                string            `json:"LifeState"`
	TaskState                string            `json:"TaskState"`
	VpcId                    int64             `json:"VpcId"`
	UniqVpcID                string            `json:"UniqVpcID"`
	Os                       string            `json:"Os"`
	ImageId                  string            `json:"ImageId"`
	ClusterCIDR              string            `json:"ClusterCIDR"`
	ServiceCIDR              string            `json:"ServiceCIDR"`
	NodeCIDRMaskSize         int               `json:"NodeCIDRMaskSize"`
	ExternalEndpoint         string            `json:"ExternalEndpoint"`
	TgwEndpoint              string            `json:"TgwEndpoint"`
	VpcLbEndpoint            string            `json:"VpcLbEndpoint"`
	MasterEndpoint           string            `json:"MasterEndpoint"`
	MasterListenPort         int               `json:"MasterListenPort"`
	IsSecure                 int               `json:"IsSecure"`
	ClusterType              int               `json:"ClusterType"`
	EtcdClusterVpcLbEndpoint string            `json:"EtcdClusterVpcLbEndpoint"`
	EtcdClusterJNSGWEndpoint string            `json:"EtcdClusterJNSGWEndpoint"`
	CreatedAt                string            `json:"CreatedAt"`
	UpdatedAt                string            `json:"UpdatedAt"`
	K8sVersion               string            `json:"K8sVersion"`
	DockerVersion            string            `json:"DockerVersion"`
	ProjectId                int               `json:"ProjectId"`
	OsCustomizeType          string            `json:"OsCustomizeType"`
	MasterUSG                string            `json:"MasterUSG"`
	WorkUSG                  string            `json:"WorkUSG"`
	RuntimeConifg            string            `json:"RuntimeConifg"`
	MetaClusterID            string            `json:"MetaClusterID"`
	MonitorStorageId         string            `json:"MonitorStorageId"`
	ExtraArgs                string            `json:"ExtraArgs"`
	Context                  string            `json:"Context"`
	DeletionProtection       bool              `json:"DeletionProtection"`
	AuditEnabled             bool              `json:"AuditEnabled"`
	ExternalNodeConfig       string            `json:"ExternalNodeConfig"`
	ParentClusterID          string            `json:"ParentClusterID"`
	CloudVendor              string            `json:"CloudVendor"`
	ClusterLevel             string            `json:"ClusterLevel"`
	AutoUpgradeClusterLevel  string            `json:"AutoUpgradeClusterLevel"`
	Annotations              map[string]string `json:"annotations,omitempty"` // EKS集群的annotations
	RawData                  interface{}       `json:"rawData,omitempty"`     // 原始数据，用于EKS集群解析
}

// WorkloadData ClickHouse中的工作负载数据结构
type WorkloadData struct {
	Region       string    `json:"Region" ch:"Region"`
	AppId        uint64    `json:"AppId" ch:"AppId"`
	ClusterId    string    `json:"ClusterId" ch:"ClusterId"`
	Name         string    `json:"Name" ch:"Name"`
	Namespace    string    `json:"Namespace" ch:"Namespace"`
	WorkloadType string    `json:"WorkloadType" ch:"WorkloadType"`
	WorkloadName string    `json:"WorkloadName" ch:"WorkloadName"`
	Data         string    `json:"Data" ch:"Data"`
	DateDay      time.Time `json:"DateDay" ch:"DateDay"`
}

// PodData ClickHouse中的Pod数据结构（用于独立集群静态Pod采集）
type PodData struct {
	Region       string    `json:"Region" ch:"Region"`
	AppId        uint64    `json:"AppId" ch:"AppId"`
	ClusterId    string    `json:"ClusterId" ch:"ClusterId"`
	Name         string    `json:"Name" ch:"Name"`
	Namespace    string    `json:"Namespace" ch:"Namespace"`
	NodeName     string    `json:"NodeName" ch:"NodeName"`
	WorkloadType string    `json:"WorkloadType" ch:"WorkloadType"`
	WorkloadName string    `json:"WorkloadName" ch:"WorkloadName"`
	Data         string    `json:"Data" ch:"Data"`
	DateDay      time.Time `json:"DateDay" ch:"DateDay"`
}

// PodCounts Pod数量统计结果
type PodCounts struct {
	TotalPodCount uint64 `json:"totalPodCount"`
	EkletPodCount uint64 `json:"ekletPodCount"`
}

// 集群类型常量（从Data字段解析）
const (
	ClusterTypeManaged     = "8" // 托管集群
	ClusterTypeIndependent = "4" // 独立集群
)

// 集群平台类型常量（ClusterType字段）
const (
	ClusterPlatformTKE = "tke" // TKE集群
	ClusterPlatformEKS = "eks" // EKS集群
)

// 组件配置
type ComponentConfig struct {
	Name            string                                                    // 组件名称
	ContainerNames  []string                                                  // 可能的容器名称列表
	WorkloadType    string                                                    // workload类型: deployment, daemonset
	IsAlwaysMeta    bool                                                      // 是否总是在Meta集群获取
	GetWorkloadName func(clusterID string, isManaged bool, isTKE bool) string // 获取workloadName的函数
}

// 组件配置映射
var ComponentConfigs = map[string]ComponentConfig{
	"kube-apiserver": {
		Name:           "kube-apiserver",
		ContainerNames: []string{"apiserver"}, // 容器名为apiserver
		WorkloadType:   "deployment",
		IsAlwaysMeta:   false, // 根据集群类型决定
		GetWorkloadName: func(clusterID string, isManaged bool, isTKE bool) string {
			if isManaged {
				return clusterID + "-apiserver" // 托管集群: cls-kihzth22-apiserver
			}
			return "kube-apiserver" // 独立集群: kube-apiserver
		},
	},
	"kube-scheduler": {
		Name:           "kube-scheduler",
		ContainerNames: []string{"scheduler"}, // 容器名为scheduler
		WorkloadType:   "deployment",
		IsAlwaysMeta:   false,
		GetWorkloadName: func(clusterID string, isManaged bool, isTKE bool) string {
			if isManaged {
				return clusterID + "-scheduler"
			}
			return "kube-scheduler"
		},
	},
	"kube-controller-manager": {
		Name:           "kube-controller-manager",
		ContainerNames: []string{"controller-manager"}, // 容器名为controller-manager
		WorkloadType:   "deployment",
		IsAlwaysMeta:   false,
		GetWorkloadName: func(clusterID string, isManaged bool, isTKE bool) string {
			if isManaged {
				return clusterID + "-controller-manager"
			}
			return "kube-controller-manager"
		},
	},
	"coredns": {
		Name:           "coredns",
		ContainerNames: []string{"coredns"}, // 容器名为coredns
		WorkloadType:   "deployment",
		IsAlwaysMeta:   false, // 用户集群组件
		GetWorkloadName: func(clusterID string, isManaged bool, isTKE bool) string {
			return "coredns"
		},
	},
	"kube-proxy": {
		Name:           "kube-proxy",
		ContainerNames: []string{"kube-proxy"}, // 容器名为kube-proxy
		WorkloadType:   "daemonset",
		IsAlwaysMeta:   false, // 用户集群组件
		GetWorkloadName: func(clusterID string, isManaged bool, isTKE bool) string {
			return "kube-proxy"
		},
	},
	"eklet": {
		Name:           "eklet",
		ContainerNames: []string{"eklet"}, // 容器名为eklet
		WorkloadType:   "deployment",
		IsAlwaysMeta:   true, // 总是在Meta集群
		GetWorkloadName: func(clusterID string, isManaged bool, isTKE bool) string {
			if isTKE {
				return "eklet" // TKE集群: eklet
			} else {
				return clusterID + "-eklet" // EKS集群: cls-xxx-eklet
			}
		},
	},
}

// 使用标准的Kubernetes API结构体

// WorkloadDetail 工作负载详细信息的通用接口
type WorkloadDetail interface {
	GetPodTemplate() corev1.PodTemplateSpec
	GetName() string
	GetNamespace() string
	GetLabels() map[string]string
	GetAnnotations() map[string]string
}

// DeploymentWorkload Deployment工作负载
type DeploymentWorkload struct {
	appsv1.Deployment
}

func (d *DeploymentWorkload) GetPodTemplate() corev1.PodTemplateSpec {
	return d.Spec.Template
}

func (d *DeploymentWorkload) GetName() string {
	return d.Name
}

func (d *DeploymentWorkload) GetNamespace() string {
	return d.Namespace
}

func (d *DeploymentWorkload) GetLabels() map[string]string {
	return d.Labels
}

func (d *DeploymentWorkload) GetAnnotations() map[string]string {
	return d.Annotations
}

// DaemonSetWorkload DaemonSet工作负载
type DaemonSetWorkload struct {
	appsv1.DaemonSet
}

func (ds *DaemonSetWorkload) GetPodTemplate() corev1.PodTemplateSpec {
	return ds.Spec.Template
}

func (ds *DaemonSetWorkload) GetName() string {
	return ds.Name
}

func (ds *DaemonSetWorkload) GetNamespace() string {
	return ds.Namespace
}

func (ds *DaemonSetWorkload) GetLabels() map[string]string {
	return ds.Labels
}

func (ds *DaemonSetWorkload) GetAnnotations() map[string]string {
	return ds.Annotations
}

// PodWorkload Pod工作负载（用于独立集群静态Pod）
type PodWorkload struct {
	corev1.Pod
}

func (p *PodWorkload) GetPodTemplate() corev1.PodTemplateSpec {
	// Pod没有PodTemplate，创建一个包含Pod Spec的模板
	return corev1.PodTemplateSpec{
		ObjectMeta: p.ObjectMeta,
		Spec:       p.Spec,
	}
}

func (p *PodWorkload) GetName() string {
	return p.Name
}

func (p *PodWorkload) GetNamespace() string {
	return p.Namespace
}

func (p *PodWorkload) GetLabels() map[string]string {
	return p.Labels
}

func (p *PodWorkload) GetAnnotations() map[string]string {
	return p.Annotations
}

// GetClusterTypeFromData 从Data字段解析集群类型（4/8）
func (c *ClusterData) GetClusterTypeFromData() string {
	if c.Data == "" {
		return ""
	}

	var dataMap map[string]interface{}
	if err := json.Unmarshal([]byte(c.Data), &dataMap); err != nil {
		return ""
	}

	if clusterType, ok := dataMap["ClusterType"]; ok {
		if typeStr, ok := clusterType.(string); ok {
			return typeStr
		}
		// 处理数字类型
		if typeNum, ok := clusterType.(float64); ok {
			return fmt.Sprintf("%.0f", typeNum)
		}
	}

	return ""
}

// IsManaged 判断是否为托管集群
func (c *ClusterData) IsManaged() bool {
	// EKS集群默认为托管集群
	if c.ClusterType == "eks" {
		return true
	}

	// TKE集群根据Data中的ClusterType判断
	clusterType := c.GetClusterTypeFromData()
	return clusterType == ClusterTypeManaged
}

// IsTKE 判断是否为TKE集群
func (c *ClusterData) IsTKE() bool {
	return c.ClusterType == ClusterPlatformTKE
}

// IsEKS 判断是否为EKS集群
func (c *ClusterData) IsEKS() bool {
	return c.ClusterType == ClusterPlatformEKS
}

// Context 集群上下文信息
type Context struct {
	CreateCtx *CreateContext `json:"CreateCtx"`
	DeleteCtx *DeleteContext `json:"DeleteCtx"`
}

// CreateContext 创建上下文信息
type CreateContext struct {
	Uin             string      `json:"Uin"`
	SubAccountUin   string      `json:"SubAccountUin"`
	ReadyToInit     bool        `json:"ReadyToInit"`
	MasterCount     int         `json:"MasterCount"`
	UseRBACAuth     bool        `json:"UseRBACAuth"`
	ExtensionAddons interface{} `json:"ExtensionAddons"`
}

// DeleteContext 删除上下文信息
type DeleteContext struct {
	// 根据需要添加字段
}

// ComponentVersion 组件版本信息
type ComponentVersion struct {
	Name   string          `json:"name"`
	Image  string          `json:"image"`
	Tag    string          `json:"tag"`
	Args   []string        `json:"args"`
	Env    []corev1.EnvVar `json:"env"`    // 环境变量
	Type   string          `json:"type"`   // deployment, daemonset
	Source string          `json:"source"` // user, meta
}

// ESClusterDocument ES中的集群文档结构
type ESClusterDocument struct {
	// 基础信息 - 来自ClickHouse
	ClusterID     string `json:"clusterID"` // 修正：ES中的字段名是clusterID
	ClusterName   string `json:"clusterName"`
	ClusterType   string `json:"clusterType"`
	ClusterStatus string `json:"clusterStatus"`
	Region        string `json:"region"`
	AppID         string `json:"appId"`
	K8sVersion    string `json:"k8sVersion"` // 统一使用K8sVersion字段

	// TKE集群管理类型 - 来自ClickHouse Data字段解析
	ClusterManagementType string `json:"clusterManagementType,omitempty"`

	// 账户信息 - 来自ES现有数据
	AccountName  string `json:"accountName"`
	AccountType  string `json:"accountType"`
	AccountLevel string `json:"accountLevel"`
	IsBigUser    int    `json:"isBigUser"`

	// 创建者信息 - 来自ClickHouse Context.CreateCtx解析
	Uin              string    `json:"uin"`
	SubAccountUin    string    `json:"subAccountUin"`
	ClusterCreatedAt time.Time `json:"clusterCreatedAt"`
	ProductName      string    `json:"productName,omitempty"` // EKS集群的产品名称

	// 资源信息 - 来自ClickHouse查询或ES现有数据
	ClusterLevel string `json:"clusterLevel"`
	NodeCount    int    `json:"nodeCount"` // 从ClickHouse node表查询
	CPUCount     int    `json:"cpuCount"`
	MemCount     int    `json:"memCount"`
	PodCount     int    `json:"podCount"`    // 总Pod数量，从ClickHouse pod表查询
	EksPodCount  int    `json:"eksPodCount"` // 超级节点Pod数量

	// 网络信息 - 来自ClickHouse
	VpcID       string `json:"vpcId"`
	ClusterCIDR string `json:"clusterCIDR"`
	ServiceCIDR string `json:"serviceCIDR"`

	// Meta集群信息 - 来自ClickHouse
	MetaClusterID string `json:"metaClusterId"`

	// 组件版本信息 - 来自workload数据解析
	ComponentVersion map[string]ComponentVersion `json:"componentVersion"`

	// App信息 - 来自MySQL（仅TKE集群）
	AddonInfo map[string]interface{} `json:"addonInfo,omitempty"`

	// 动态标签 - 来自ES现有数据
	Tags map[string]interface{} `json:"tags"`

	// CMDB元数据 - 来自ES现有数据
	CmdbMd map[string]interface{} `json:"cmdbMd"`

	// 时间信息
	Date      string    `json:"date"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}
