package config

import (
	"fmt"
	"os"

	"gopkg.in/yaml.v3"
)

// Config 应用配置
type Config struct {
	ClickHouse       ClickHouseConfig           `yaml:"clickhouse"`
	MySQL            MySQLConfig                `yaml:"mysql"`
	Elasticsearch    ElasticsearchConfig        `yaml:"elasticsearch"`
	Sync             SyncConfig                 `yaml:"sync"`
	Logging          LoggingConfig              `yaml:"logging"`
	Components       map[string]ComponentConfig `yaml:"components"`
	GlobalComponents GlobalComponentConfig      `yaml:"global_components"`
}

// ClickHouseConfig ClickHouse配置
type ClickHouseConfig struct {
	Host            string `yaml:"host"`
	Port            int    `yaml:"port"`
	Database        string `yaml:"database"`
	Username        string `yaml:"username"`
	Password        string `yaml:"password"`
	MaxOpenConns    int    `yaml:"max_open_conns"`
	MaxIdleConns    int    `yaml:"max_idle_conns"`
	ConnMaxLifetime string `yaml:"conn_max_lifetime"`
	DialTimeout     string `yaml:"dial_timeout"`
	ReadTimeout     string `yaml:"read_timeout"`
	WriteTimeout    string `yaml:"write_timeout"`
	Compress        bool   `yaml:"compress"`
	Debug           bool   `yaml:"debug"`
}

// MySQLConfig MySQL配置
type MySQLConfig struct {
	Host            string `yaml:"host"`
	Port            int    `yaml:"port"`
	Database        string `yaml:"database"`
	Username        string `yaml:"username"`
	Password        string `yaml:"password"`
	MaxOpenConns    int    `yaml:"max_open_conns"`
	MaxIdleConns    int    `yaml:"max_idle_conns"`
	ConnMaxLifetime string `yaml:"conn_max_lifetime"`
}

// ElasticsearchConfig Elasticsearch配置
type ElasticsearchConfig struct {
	ReadConfig  ESInstanceConfig `yaml:"read_config"`
	WriteConfig ESInstanceConfig `yaml:"write_config"`
	IndexPrefix string           `yaml:"index_prefix"`
	BulkSize    int              `yaml:"bulk_size"`
	BulkWorkers int              `yaml:"bulk_workers"`
	Timeout     string           `yaml:"timeout"`
}

// ESInstanceConfig ES实例配置
type ESInstanceConfig struct {
	Host       string `yaml:"host"`
	Port       int    `yaml:"port"`
	Username   string `yaml:"username"`
	Password   string `yaml:"password"`
	Scheme     string `yaml:"scheme"`
	MaxRetries int    `yaml:"max_retries"`
	Timeout    string `yaml:"timeout"`
}

// SyncConfig 同步配置
type SyncConfig struct {
	BatchSize          int    `yaml:"batch_size"`
	MaxWorkers         int    `yaml:"max_workers"`
	RetryTimes         int    `yaml:"retry_times"`
	RetryInterval      string `yaml:"retry_interval"`
	ComponentBatchSize int    `yaml:"component_batch_size"`
	ESQueryOffset      int    `yaml:"es_query_offset"` // ES查询时间偏移（天）
	CHQueryOffset      int    `yaml:"ch_query_offset"` // ClickHouse查询时间偏移（天）
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level  string `yaml:"level"`
	Format string `yaml:"format"`
	Output string `yaml:"output"`
}

// Load 加载配置文件
func Load(configPath string) (*Config, error) {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 设置默认值
	setDefaults(&config)

	return &config, nil
}

// setDefaults 设置默认值
func setDefaults(config *Config) {
	// ClickHouse默认值
	if config.ClickHouse.Port == 0 {
		config.ClickHouse.Port = 9000
	}
	if config.ClickHouse.MaxOpenConns == 0 {
		config.ClickHouse.MaxOpenConns = 10
	}
	if config.ClickHouse.MaxIdleConns == 0 {
		config.ClickHouse.MaxIdleConns = 5
	}
	if config.ClickHouse.ConnMaxLifetime == "" {
		config.ClickHouse.ConnMaxLifetime = "300s"
	}
	if config.ClickHouse.DialTimeout == "" {
		config.ClickHouse.DialTimeout = "10s"
	}
	if config.ClickHouse.ReadTimeout == "" {
		config.ClickHouse.ReadTimeout = "30s"
	}
	if config.ClickHouse.WriteTimeout == "" {
		config.ClickHouse.WriteTimeout = "30s"
	}

	// Elasticsearch默认值
	if config.Elasticsearch.ReadConfig.Port == 0 {
		config.Elasticsearch.ReadConfig.Port = 9200
	}
	if config.Elasticsearch.WriteConfig.Port == 0 {
		config.Elasticsearch.WriteConfig.Port = 9200
	}
	if config.Elasticsearch.ReadConfig.Scheme == "" {
		config.Elasticsearch.ReadConfig.Scheme = "http"
	}
	if config.Elasticsearch.WriteConfig.Scheme == "" {
		config.Elasticsearch.WriteConfig.Scheme = "http"
	}
	if config.Elasticsearch.IndexPrefix == "" {
		config.Elasticsearch.IndexPrefix = "cmdb"
	}
	if config.Elasticsearch.BulkSize == 0 {
		config.Elasticsearch.BulkSize = 100
	}
	if config.Elasticsearch.BulkWorkers == 0 {
		config.Elasticsearch.BulkWorkers = 4
	}
	if config.Elasticsearch.Timeout == "" {
		config.Elasticsearch.Timeout = "30s"
	}

	// 同步配置默认值
	if config.Sync.BatchSize == 0 {
		config.Sync.BatchSize = 100
	}
	if config.Sync.MaxWorkers == 0 {
		config.Sync.MaxWorkers = 5
	}
	if config.Sync.RetryTimes == 0 {
		config.Sync.RetryTimes = 3
	}
	if config.Sync.RetryInterval == "" {
		config.Sync.RetryInterval = "60s"
	}
	if config.Sync.ComponentBatchSize == 0 {
		config.Sync.ComponentBatchSize = 100
	}
	if config.Sync.ESQueryOffset == 0 {
		config.Sync.ESQueryOffset = -2 // 默认查询前2天的ES数据
	}
	if config.Sync.CHQueryOffset == 0 {
		config.Sync.CHQueryOffset = -1 // 默认查询前1天的ClickHouse数据
	}

	// 日志配置默认值
	if config.Logging.Level == "" {
		config.Logging.Level = "info"
	}
	if config.Logging.Format == "" {
		config.Logging.Format = "json"
	}
	if config.Logging.Output == "" {
		config.Logging.Output = "stdout"
	}
}

// ComponentsConfig 组件配置
type ComponentsConfig struct {
	Components map[string]ComponentConfig `yaml:"components"`
	Global     GlobalComponentConfig      `yaml:"global"`
}

// ComponentConfig 单个组件配置
type ComponentConfig struct {
	ContainerNames []string        `yaml:"container_names"`
	ComponentType  string          `yaml:"component_type"` // control-plane, user, special
	Platforms      PlatformConfigs `yaml:"platforms"`
	Description    string          `yaml:"description"`
}

// PlatformConfigs 平台配置
type PlatformConfigs struct {
	TKE TKEPlatformConfig `yaml:"tke"`
	EKS EKSPlatformConfig `yaml:"eks"`
}

// TKEPlatformConfig TKE平台配置
type TKEPlatformConfig struct {
	Managed     *TKEClusterConfig `yaml:"managed,omitempty"`     // 托管集群配置
	Independent *TKEClusterConfig `yaml:"independent,omitempty"` // 独立集群配置
	// 用户组件和特殊组件可能不区分托管/独立
	WorkloadName string `yaml:"workload_name,omitempty"` // 统一配置
	QuerySource  string `yaml:"query_source,omitempty"`  // 统一配置
}

// TKEClusterConfig TKE集群配置
type TKEClusterConfig struct {
	WorkloadName string `yaml:"workload_name"`
	QuerySource  string `yaml:"query_source"`
}

// EKSPlatformConfig EKS平台配置
type EKSPlatformConfig struct {
	WorkloadName string `yaml:"workload_name"`
	QuerySource  string `yaml:"query_source"`
}

// GlobalComponentConfig 全局组件配置
type GlobalComponentConfig struct {
	DefaultQuerySource         string `yaml:"default_query_source"`
	EnableComponentCollection  bool   `yaml:"enable_component_collection"`
	ComponentCollectionTimeout int    `yaml:"component_collection_timeout"`
}
