package clickhouse

import (
	"context"
	"fmt"
	"strings"
	"time"

	"git.woa.com/kmetis/cmdb/internal/config"
	"git.woa.com/kmetis/cmdb/internal/model"
	"git.woa.com/kmetis/cmdb/pkg/logger"

	"github.com/ClickHouse/clickhouse-go/v2"
)

// Client ClickHouse客户端
type Client struct {
	conn   clickhouse.Conn
	logger logger.Logger
}

// NewClient 创建ClickHouse客户端
func NewClient(cfg config.ClickHouseConfig, logger logger.Logger) (*Client, error) {
	// 解析超时时间
	dialTimeout, err := time.ParseDuration(cfg.DialTimeout)
	if err != nil {
		dialTimeout = 10 * time.Second
	}

	readTimeout, err := time.ParseDuration(cfg.ReadTimeout)
	if err != nil {
		readTimeout = 30 * time.Second
	}

	// 创建连接
	conn, err := clickhouse.Open(&clickhouse.Options{
		Addr: []string{fmt.Sprintf("%s:%d", cfg.Host, cfg.Port)},
		Auth: clickhouse.Auth{
			Database: cfg.Database,
			Username: cfg.Username,
			Password: cfg.Password,
		},
		DialTimeout: dialTimeout,
		ReadTimeout: readTimeout,
		Compression: &clickhouse.Compression{
			Method: clickhouse.CompressionLZ4,
		},
		Settings: clickhouse.Settings{
			"max_execution_time": 60,
		},
		MaxOpenConns: cfg.MaxOpenConns,
		MaxIdleConns: cfg.MaxIdleConns,
		Debug:        cfg.Debug,
	})

	if err != nil {
		return nil, fmt.Errorf("连接ClickHouse失败: %w", err)
	}

	// 测试连接
	if err := conn.Ping(context.Background()); err != nil {
		return nil, fmt.Errorf("ClickHouse连接测试失败: %w", err)
	}

	logger.Info("ClickHouse连接成功")

	return &Client{
		conn:   conn,
		logger: logger,
	}, nil
}

// GetClustersByDate 根据日期获取集群数据
func (c *Client) GetClustersByDate(ctx context.Context, date time.Time) ([]model.ClusterData, error) {
	dateStr := date.Format("2006-01-02")

	query := `
		SELECT Region, AppId, ClusterId, Name, ClusterType, Version, Status, Data, DateDay
		FROM cluster 
		WHERE DateDay = ?
		ORDER BY ClusterId
	`

	rows, err := c.conn.Query(ctx, query, dateStr)
	if err != nil {
		return nil, fmt.Errorf("查询集群数据失败: %w", err)
	}
	defer rows.Close()

	var clusters []model.ClusterData
	for rows.Next() {
		var cluster model.ClusterData
		err := rows.Scan(
			&cluster.Region,
			&cluster.AppId,
			&cluster.ClusterId,
			&cluster.Name,
			&cluster.ClusterType,
			&cluster.Version,
			&cluster.Status,
			&cluster.Data,
			&cluster.DateDay,
		)
		if err != nil {
			c.logger.Errorf("扫描集群数据失败: %v", err)
			continue
		}
		clusters = append(clusters, cluster)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历集群数据失败: %w", err)
	}

	c.logger.Infof("从ClickHouse获取到 %d 个集群数据", len(clusters))
	return clusters, nil
}

// GetBatchNodeCounts 批量获取集群节点数量统计
func (c *Client) GetBatchNodeCounts(ctx context.Context, date time.Time, clusterIds []string) (map[string]uint64, error) {
	if len(clusterIds) == 0 {
		return make(map[string]uint64), nil
	}

	dateStr := date.Format("2006-01-02")

	// 构建批量查询SQL
	query := `
		SELECT ClusterId, COUNT(*) as node_count
		FROM node
		WHERE DateDay = ?
		AND ClusterId IN (?)
		GROUP BY ClusterId
	`

	// 构建参数
	clusterIdArgs := make([]interface{}, len(clusterIds))
	for i, id := range clusterIds {
		clusterIdArgs[i] = id
	}

	c.logger.Debugf("执行节点数量批量查询: 集群数量=%d", len(clusterIds))
	c.logger.Debugf("查询参数: date=%s, clusters=%v", dateStr, clusterIds)

	rows, err := c.conn.Query(ctx, query, dateStr, clusterIdArgs)
	if err != nil {
		c.logger.Errorf("节点数量查询失败: %v", err)
		return nil, fmt.Errorf("查询节点数量失败: %w", err)
	}
	defer rows.Close()

	result := make(map[string]uint64)
	totalNodes := uint64(0)

	for rows.Next() {
		var clusterID string
		var nodeCount uint64

		if err := rows.Scan(&clusterID, &nodeCount); err != nil {
			c.logger.Errorf("扫描节点数量结果失败: %v", err)
			continue
		}

		result[clusterID] = nodeCount
		totalNodes += nodeCount
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历节点数量结果失败: %w", err)
	}

	c.logger.Infof("节点数量批量查询完成: 集群数量=%d, 有节点的集群=%d, 总节点数=%d",
		len(clusterIds), len(result), totalNodes)

	// 为没有节点的集群设置0
	for _, clusterID := range clusterIds {
		if _, exists := result[clusterID]; !exists {
			result[clusterID] = 0
		}
	}

	return result, nil
}

// GetBatchPodCounts 批量获取集群Pod数量统计（统一方法）
func (c *Client) GetBatchPodCounts(ctx context.Context, date time.Time, clusterIds []string, isTKE bool) (map[string]model.PodCounts, error) {
	if len(clusterIds) == 0 {
		return make(map[string]model.PodCounts), nil
	}

	dateStr := date.Format("2006-01-02")

	// 构建参数
	clusterIdArgs := make([]interface{}, len(clusterIds))
	for i, id := range clusterIds {
		clusterIdArgs[i] = id
	}

	clusterType := "EKS"
	if isTKE {
		clusterType = "TKE"
	}
	c.logger.Debugf("执行%s Pod数量批量查询: 集群数量=%d", clusterType, len(clusterIds))

	// 1. 查询总Pod数量
	totalQuery := `
		SELECT ClusterId, COUNT(*) as total_pod_count
		FROM pod
		WHERE DateDay = ?
		AND ClusterId IN (?)
		GROUP BY ClusterId
	`

	totalRows, err := c.conn.Query(ctx, totalQuery, dateStr, clusterIdArgs)
	if err != nil {
		c.logger.Errorf("总Pod数量查询失败: %v", err)
		return nil, fmt.Errorf("查询总Pod数量失败: %w", err)
	}
	defer totalRows.Close()

	result := make(map[string]model.PodCounts)

	// 处理总Pod数量结果
	for totalRows.Next() {
		var clusterID string
		var totalCount uint64

		if err := totalRows.Scan(&clusterID, &totalCount); err != nil {
			c.logger.Errorf("扫描总Pod数量结果失败: %v", err)
			continue
		}

		counts := result[clusterID]
		counts.TotalPodCount = totalCount
		result[clusterID] = counts
	}

	if err := totalRows.Err(); err != nil {
		return nil, fmt.Errorf("遍历总Pod数量结果失败: %w", err)
	}

	// 2. 查询eklet Pod数量（仅TKE集群需要区分）
	if isTKE {
		ekletQuery := `
			SELECT ClusterId, COUNT(*) as eklet_pod_count
			FROM pod
			WHERE DateDay = ?
			AND ClusterId IN (?)
			AND JSONExtractString(Data, 'metadata', 'annotations', 'tke.cloud.tencent.com/pod-type') = 'eklet'
			GROUP BY ClusterId
		`

		ekletRows, err := c.conn.Query(ctx, ekletQuery, dateStr, clusterIdArgs)
		if err != nil {
			c.logger.Errorf("eklet Pod数量查询失败: %v", err)
			return nil, fmt.Errorf("查询eklet Pod数量失败: %w", err)
		}
		defer ekletRows.Close()

		// 处理eklet Pod数量结果
		for ekletRows.Next() {
			var clusterID string
			var ekletCount uint64

			if err := ekletRows.Scan(&clusterID, &ekletCount); err != nil {
				c.logger.Errorf("扫描eklet Pod数量结果失败: %v", err)
				continue
			}

			counts := result[clusterID]
			counts.EkletPodCount = ekletCount
			result[clusterID] = counts
		}

		if err := ekletRows.Err(); err != nil {
			return nil, fmt.Errorf("遍历eklet Pod数量结果失败: %w", err)
		}
	} else {
		// EKS集群：eksPodCount与podCount相同
		for clusterID, counts := range result {
			counts.EkletPodCount = counts.TotalPodCount
			result[clusterID] = counts
		}
	}

	totalPods := uint64(0)
	totalEkletPods := uint64(0)
	for _, counts := range result {
		totalPods += counts.TotalPodCount
		totalEkletPods += counts.EkletPodCount
	}

	c.logger.Infof("%s Pod数量批量查询完成: 集群数量=%d, 有Pod的集群=%d, 总Pod数=%d, 总eklet Pod数=%d",
		clusterType, len(clusterIds), len(result), totalPods, totalEkletPods)

	// 为没有Pod的集群设置0
	for _, clusterID := range clusterIds {
		if _, exists := result[clusterID]; !exists {
			result[clusterID] = model.PodCounts{TotalPodCount: 0, EkletPodCount: 0}
		}
	}

	return result, nil
}

// GetStaticPods 获取独立集群的静态Pod（控制面组件）
func (c *Client) GetStaticPods(ctx context.Context, date time.Time, clusterIds []string, componentNames []string) ([]model.PodData, error) {
	if len(clusterIds) == 0 || len(componentNames) == 0 {
		c.logger.Warnf("静态Pod查询参数为空: clusterIds=%d, componentNames=%d",
			len(clusterIds), len(componentNames))
		return []model.PodData{}, nil
	}

	dateStr := date.Format("2006-01-02")

	// 构建组件名称的LIKE条件
	var likeConditions []string
	for _, componentName := range componentNames {
		likeConditions = append(likeConditions, fmt.Sprintf("Name LIKE '%s-%%'", componentName))
	}
	likeClause := strings.Join(likeConditions, " OR ")

	query := fmt.Sprintf(`
		SELECT Region, AppId, ClusterId, Name, Namespace, NodeName, WorkloadType, WorkloadName, Data, DateDay
		FROM pod
		WHERE DateDay = ?
		AND ClusterId IN (?)
		AND Namespace = 'kube-system'
		AND (%s)
		ORDER BY ClusterId, Name
	`, likeClause)

	// 构建参数
	clusterIdArgs := make([]interface{}, len(clusterIds))
	for i, id := range clusterIds {
		clusterIdArgs[i] = id
	}

	c.logger.Infof("查询独立集群静态Pod - 日期: %s, 集群: %v, 组件: %v",
		dateStr, clusterIds, componentNames)
	c.logger.Debugf("执行静态Pod查询SQL: %s", query)

	rows, err := c.conn.Query(ctx, query, dateStr, clusterIdArgs)
	if err != nil {
		c.logger.Errorf("静态Pod查询失败: %v", err)
		return nil, fmt.Errorf("查询静态Pod失败: %w", err)
	}
	defer rows.Close()

	var pods []model.PodData
	for rows.Next() {
		var pod model.PodData
		err := rows.Scan(
			&pod.Region,
			&pod.AppId,
			&pod.ClusterId,
			&pod.Name,
			&pod.Namespace,
			&pod.NodeName,
			&pod.WorkloadType,
			&pod.WorkloadName,
			&pod.Data,
			&pod.DateDay,
		)
		if err != nil {
			c.logger.Errorf("扫描静态Pod数据失败: %v", err)
			continue
		}
		pods = append(pods, pod)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历静态Pod数据失败: %w", err)
	}

	c.logger.Infof("从ClickHouse获取到 %d 个静态Pod数据", len(pods))
	return pods, nil
}

// GetUserWorkloads 获取用户集群的工作负载
func (c *Client) GetUserWorkloads(ctx context.Context, date time.Time, clusterIds []string, workloadNames []string) ([]model.WorkloadData, error) {
	if len(clusterIds) == 0 || len(workloadNames) == 0 {
		c.logger.Warnf("用户工作负载查询参数为空: clusterIds=%d, workloadNames=%d",
			len(clusterIds), len(workloadNames))
		return []model.WorkloadData{}, nil
	}

	dateStr := date.Format("2006-01-02")

	c.logger.Infof("查询用户工作负载 - 日期: %s, 集群: %v, workloadNames: %v",
		dateStr, clusterIds, workloadNames)

	query := `
		SELECT Region, AppId, ClusterId, Name, Namespace, WorkloadType, WorkloadName, Data, DateDay
		FROM workload
		WHERE DateDay = ?
		AND ClusterId IN (?)
		AND WorkloadName IN (?)
		ORDER BY ClusterId, WorkloadName
	`

	// 构建参数
	clusterIdArgs := make([]interface{}, len(clusterIds))
	for i, id := range clusterIds {
		clusterIdArgs[i] = id
	}

	workloadNameArgs := make([]interface{}, len(workloadNames))
	for i, name := range workloadNames {
		workloadNameArgs[i] = name
	}

	c.logger.Debugf("执行用户查询SQL: %s", query)
	c.logger.Debugf("查询参数: date=%s, clusters=%v, workloadNames=%v",
		dateStr, clusterIdArgs, workloadNameArgs)

	rows, err := c.conn.Query(ctx, query, dateStr, clusterIdArgs, workloadNameArgs)
	if err != nil {
		c.logger.Errorf("用户工作负载查询失败: %v", err)
		return nil, fmt.Errorf("查询用户工作负载失败: %w", err)
	}
	defer rows.Close()

	var workloads []model.WorkloadData
	for rows.Next() {
		var workload model.WorkloadData
		err := rows.Scan(
			&workload.Region,
			&workload.AppId,
			&workload.ClusterId,
			&workload.Name,
			&workload.Namespace,
			&workload.WorkloadType,
			&workload.WorkloadName,
			&workload.Data,
			&workload.DateDay,
		)
		if err != nil {
			c.logger.Errorf("扫描用户工作负载数据失败: %v", err)
			continue
		}

		c.logger.Debugf("找到用户workload: ClusterId=%s, Name=%s, Namespace=%s, WorkloadName=%s, WorkloadType=%s",
			workload.ClusterId, workload.Name, workload.Namespace, workload.WorkloadName, workload.WorkloadType)

		workloads = append(workloads, workload)
	}

	if err := rows.Err(); err != nil {
		c.logger.Errorf("遍历用户工作负载数据失败: %v", err)
		return nil, fmt.Errorf("遍历用户工作负载数据失败: %w", err)
	}

	c.logger.Infof("从ClickHouse获取到 %d 个用户工作负载数据", len(workloads))

	// 详细列出找到的workload
	for i, workload := range workloads {
		c.logger.Infof("  用户workload %d: %s/%s (type: %s, workloadName: %s)",
			i+1, workload.Namespace, workload.Name, workload.WorkloadType, workload.WorkloadName)
	}

	return workloads, nil
}

// GetMetaWorkloads 获取Meta集群的工作负载
func (c *Client) GetMetaWorkloads(ctx context.Context, date time.Time, metaClusterIds []string, namespaces []string, workloadNames []string) ([]model.WorkloadData, error) {
	if len(metaClusterIds) == 0 || len(namespaces) == 0 || len(workloadNames) == 0 {
		c.logger.Warnf("Meta工作负载查询参数为空: metaClusterIds=%d, namespaces=%d, workloadNames=%d",
			len(metaClusterIds), len(namespaces), len(workloadNames))
		return []model.WorkloadData{}, nil
	}

	dateStr := date.Format("2006-01-02")

	c.logger.Infof("查询Meta工作负载 - 日期: %s, Meta集群: %v, 命名空间: %v, workloadNames: %v",
		dateStr, metaClusterIds, namespaces, workloadNames)

	query := `
		SELECT Region, AppId, ClusterId, Name, Namespace, WorkloadType, WorkloadName, Data, DateDay
		FROM workload
		WHERE DateDay = ?
		AND ClusterId IN (?)
		AND Namespace IN (?)
		AND WorkloadName IN (?)
		ORDER BY ClusterId, Namespace, WorkloadName
	`

	// 构建参数
	metaClusterArgs := make([]interface{}, len(metaClusterIds))
	for i, id := range metaClusterIds {
		metaClusterArgs[i] = id
	}

	namespaceArgs := make([]interface{}, len(namespaces))
	for i, ns := range namespaces {
		namespaceArgs[i] = ns
	}

	workloadNameArgs := make([]interface{}, len(workloadNames))
	for i, name := range workloadNames {
		workloadNameArgs[i] = name
	}

	c.logger.Debugf("执行Meta查询SQL: %s", query)
	c.logger.Debugf("查询参数: date=%s, metaClusters=%v, namespaces=%v, workloadNames=%v",
		dateStr, metaClusterArgs, namespaceArgs, workloadNameArgs)

	rows, err := c.conn.Query(ctx, query, dateStr, metaClusterArgs, namespaceArgs, workloadNameArgs)
	if err != nil {
		c.logger.Errorf("Meta工作负载查询失败: %v", err)
		return nil, fmt.Errorf("查询Meta工作负载失败: %w", err)
	}
	defer rows.Close()

	var workloads []model.WorkloadData
	for rows.Next() {
		var workload model.WorkloadData
		err := rows.Scan(
			&workload.Region,
			&workload.AppId,
			&workload.ClusterId,
			&workload.Name,
			&workload.Namespace,
			&workload.WorkloadType,
			&workload.WorkloadName,
			&workload.Data,
			&workload.DateDay,
		)
		if err != nil {
			c.logger.Errorf("扫描Meta工作负载数据失败: %v", err)
			continue
		}

		c.logger.Debugf("找到Meta workload: ClusterId=%s, Name=%s, Namespace=%s, WorkloadName=%s, WorkloadType=%s",
			workload.ClusterId, workload.Name, workload.Namespace, workload.WorkloadName, workload.WorkloadType)

		workloads = append(workloads, workload)
	}

	if err := rows.Err(); err != nil {
		c.logger.Errorf("遍历Meta工作负载数据失败: %v", err)
		return nil, fmt.Errorf("遍历Meta工作负载数据失败: %w", err)
	}

	c.logger.Infof("从ClickHouse获取到 %d 个Meta工作负载数据", len(workloads))

	// 详细列出找到的workload
	for i, workload := range workloads {
		c.logger.Infof("  Meta workload %d: %s/%s (type: %s, workloadName: %s)",
			i+1, workload.Namespace, workload.Name, workload.WorkloadType, workload.WorkloadName)
	}

	return workloads, nil
}

// Close 关闭连接
func (c *Client) Close() error {
	if c.conn != nil {
		return c.conn.Close()
	}
	return nil
}
