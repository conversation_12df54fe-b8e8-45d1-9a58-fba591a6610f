package elasticsearch

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"git.woa.com/kmetis/cmdb/internal/config"
	"git.woa.com/kmetis/cmdb/internal/model"
	"git.woa.com/kmetis/cmdb/pkg/logger"
	"github.com/elastic/go-elasticsearch/v7"
	"github.com/elastic/go-elasticsearch/v7/esapi"
)

// Client Elasticsearch客户端
type Client struct {
	readClient  *elasticsearch.Client
	writeClient *elasticsearch.Client
	indexPrefix string
	logger      logger.Logger
}

// NewClient 创建Elasticsearch客户端
func NewClient(cfg config.ElasticsearchConfig, logger logger.Logger) (*Client, error) {
	// 创建读取客户端
	readClient, err := createESClient(cfg.ReadConfig)
	if err != nil {
		return nil, fmt.Errorf("创建ES读取客户端失败: %w", err)
	}

	// 创建写入客户端
	writeClient, err := createESClient(cfg.WriteConfig)
	if err != nil {
		return nil, fmt.Errorf("创建ES写入客户端失败: %w", err)
	}

	// 测试连接
	if err := testESConnection(readClient, "读取"); err != nil {
		return nil, err
	}

	if err := testESConnection(writeClient, "写入"); err != nil {
		return nil, err
	}

	logger.Info("Elasticsearch连接成功")

	return &Client{
		readClient:  readClient,
		writeClient: writeClient,
		indexPrefix: cfg.IndexPrefix,
		logger:      logger,
	}, nil
}

// createESClient 创建ES客户端
func createESClient(cfg config.ESInstanceConfig) (*elasticsearch.Client, error) {
	esConfig := elasticsearch.Config{
		Addresses: []string{
			fmt.Sprintf("%s://%s:%d", cfg.Scheme, cfg.Host, cfg.Port),
		},
		Username: cfg.Username,
		Password: cfg.Password,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}

	return elasticsearch.NewClient(esConfig)
}

// testESConnection 测试ES连接
func testESConnection(client *elasticsearch.Client, clientType string) error {
	res, err := client.Info()
	if err != nil {
		return fmt.Errorf("ES%s客户端连接测试失败: %w", clientType, err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("ES%s客户端连接测试失败: %s", clientType, res.String())
	}

	return nil
}

// GetIndexName 获取索引名称（兼容旧版本）
func (c *Client) GetIndexName(date time.Time) string {
	return c.GetIndexNameByType(date, "tke") // 默认使用tke
}

// GetIndexNameByType 根据集群类型和日期获取索引名称（高性能方案）
func (c *Client) GetIndexNameByType(date time.Time, clusterType string) string {
	var prefix string
	switch clusterType {
	case "tke":
		prefix = "starship-tke-cluster" // 添加starship前缀
	case "eks":
		prefix = "starship-eks-cluster" // 添加starship前缀
	default:
		prefix = "starship-tke-cluster" // 默认使用tke，添加starship前缀
	}
	return fmt.Sprintf("%s-%s", prefix, date.Format("2006-01-02"))
}

// getReadESIndexName 获取读取ES的索引名（使用别名）
func (c *Client) getReadESIndexName(clusterType string) string {
	switch clusterType {
	case "tke":
		return "tke-cluster"
	case "eks":
		return "eks-cluster"
	default:
		return "tke-cluster" // 默认使用tke
	}
}

// GetExistingClusters 获取ES中已存在的集群数据
func (c *Client) GetExistingClusters(ctx context.Context, date time.Time) (map[string]model.ESClusterDocument, error) {
	return c.GetExistingClustersWithOffset(ctx, date, -2) // 默认查询前2天的数据
}

// GetExistingClustersWithOffset 获取ES中已存在的集群数据（支持时间偏移）
func (c *Client) GetExistingClustersWithOffset(ctx context.Context, date time.Time, dayOffset int) (map[string]model.ESClusterDocument, error) {
	// 计算实际查询的日期
	queryDate := date.AddDate(0, 0, dayOffset)
	indexName := c.GetIndexName(queryDate)

	c.logger.Infof("查询ES数据: 同步日期=%s, 查询日期=%s (偏移%d天), 索引=%s",
		date.Format("2006-01-02"), queryDate.Format("2006-01-02"), dayOffset, indexName)

	// 检查索引是否存在
	exists, err := c.indexExists(indexName)
	if err != nil {
		return nil, fmt.Errorf("检查索引存在性失败: %w", err)
	}

	if !exists {
		c.logger.Infof("索引 %s 不存在，返回空结果", indexName)
		return make(map[string]model.ESClusterDocument), nil
	}

	// 使用scroll API分页查询所有数据
	return c.scrollAllClusters(ctx, indexName)
}

// GetExistingClustersByIds 根据集群ID列表获取ES中的集群数据
func (c *Client) GetExistingClustersByIds(ctx context.Context, date time.Time, clusterIds []string, dayOffset int, clusterType string) (map[string]model.ESClusterDocument, error) {
	if len(clusterIds) == 0 {
		return make(map[string]model.ESClusterDocument), nil
	}

	// 计算实际查询的日期
	queryDate := date.AddDate(0, 0, dayOffset)
	indexName := c.GetIndexNameByType(queryDate, clusterType)

	c.logger.Infof("根据集群ID查询ES数据: 同步日期=%s, 查询日期=%s (偏移%d天), 集群数量=%d, 索引=%s",
		date.Format("2006-01-02"), queryDate.Format("2006-01-02"), dayOffset, len(clusterIds), indexName)

	// 检查索引是否存在
	exists, err := c.indexExists(indexName)
	if err != nil {
		return nil, fmt.Errorf("检查索引存在性失败: %w", err)
	}

	if !exists {
		c.logger.Infof("索引 %s 不存在，返回空结果", indexName)
		return make(map[string]model.ESClusterDocument), nil
	}

	// 分批查询（每批1000个ID）
	result := make(map[string]model.ESClusterDocument)
	batchSize := 1000

	for i := 0; i < len(clusterIds); i += batchSize {
		end := i + batchSize
		if end > len(clusterIds) {
			end = len(clusterIds)
		}

		batchIds := clusterIds[i:end]
		batchResult, err := c.queryClustersByIds(ctx, indexName, batchIds)
		if err != nil {
			return nil, fmt.Errorf("批量查询集群失败: %w", err)
		}

		// 合并结果
		for id, doc := range batchResult {
			result[id] = doc
		}
	}

	c.logger.Infof("从ES获取到 %d 个已存在的集群数据", len(result))
	return result, nil
}

// GetExistingTagsFromWriteES 从写入ES获取集群的动态标签（用于保留标签）
func (c *Client) GetExistingTagsFromWriteES(ctx context.Context, date time.Time, clusterIds []string, dayOffset int, clusterType string) (map[string]model.ESClusterDocument, error) {
	if len(clusterIds) == 0 {
		return make(map[string]model.ESClusterDocument), nil
	}

	// 计算实际查询的日期
	queryDate := date.AddDate(0, 0, dayOffset)
	indexName := c.GetIndexNameByType(queryDate, clusterType)

	c.logger.Infof("从写入ES获取动态标签 - 日期: %s, 查询日期: %s (偏移%d天), 集群数量: %d, 索引: %s",
		date.Format("2006-01-02"), queryDate.Format("2006-01-02"), dayOffset, len(clusterIds), indexName)

	// 检查索引是否存在
	exists, err := c.indexExists(indexName)
	if err != nil {
		c.logger.Warnf("检查写入ES索引存在性失败: %v，继续处理", err)
	}

	if !exists {
		c.logger.Infof("写入ES索引 %s 不存在（可能是第一次运行），返回空结果", indexName)
		return make(map[string]model.ESClusterDocument), nil
	}

	// 分批查询（每批1000个ID）
	result := make(map[string]model.ESClusterDocument)
	batchSize := 1000

	for i := 0; i < len(clusterIds); i += batchSize {
		end := i + batchSize
		if end > len(clusterIds) {
			end = len(clusterIds)
		}

		batchIds := clusterIds[i:end]
		batchResult, err := c.queryTagsFromWriteES(ctx, indexName, batchIds)
		if err != nil {
			return nil, fmt.Errorf("批量查询写入ES标签失败: %w", err)
		}

		// 合并结果
		for id, doc := range batchResult {
			result[id] = doc
		}
	}

	c.logger.Infof("从写入ES获取到 %d 个集群的动态标签", len(result))
	return result, nil
}

// BulkUpsert 批量更新或插入集群数据（兼容旧版本）
func (c *Client) BulkUpsert(ctx context.Context, date time.Time, documents []model.ESClusterDocument) error {
	return c.BulkUpsertByType(ctx, date, documents, "tke") // 默认使用tke
}

// BulkUpsertByType 按集群类型批量更新或插入集群数据
func (c *Client) BulkUpsertByType(ctx context.Context, date time.Time, documents []model.ESClusterDocument, clusterType string) error {
	if len(documents) == 0 {
		return nil
	}

	indexName := c.GetIndexNameByType(date, clusterType)
	c.logger.Infof("批量写入 %d 个文档到索引: %s", len(documents), indexName)

	// 确保索引存在
	if err := c.ensureIndex(indexName); err != nil {
		return fmt.Errorf("确保索引存在失败: %w", err)
	}

	// 构建批量请求
	var buf bytes.Buffer
	for _, doc := range documents {
		// 索引操作
		indexOp := map[string]interface{}{
			"index": map[string]interface{}{
				"_index": indexName,
				"_id":    doc.ClusterID,
			},
		}

		indexOpBytes, err := json.Marshal(indexOp)
		if err != nil {
			return fmt.Errorf("序列化索引操作失败: %w", err)
		}

		buf.Write(indexOpBytes)
		buf.WriteByte('\n')

		// 文档数据
		docBytes, err := json.Marshal(doc)
		if err != nil {
			return fmt.Errorf("序列化文档失败: %w", err)
		}

		buf.Write(docBytes)
		buf.WriteByte('\n')
	}

	// 执行批量操作
	req := esapi.BulkRequest{
		Body: &buf,
	}

	res, err := req.Do(ctx, c.writeClient)
	if err != nil {
		return fmt.Errorf("执行批量操作失败: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("批量操作请求失败: %s", res.String())
	}

	// 解析响应检查错误
	var bulkResult struct {
		Errors bool                     `json:"errors"`
		Items  []map[string]interface{} `json:"items"`
	}

	if err := json.NewDecoder(res.Body).Decode(&bulkResult); err != nil {
		return fmt.Errorf("解析批量操作结果失败: %w", err)
	}

	if bulkResult.Errors {
		c.logger.Warn("批量操作中存在错误")
		for i, item := range bulkResult.Items {
			for action, result := range item {
				if resultMap, ok := result.(map[string]interface{}); ok {
					if status, exists := resultMap["status"]; exists {
						if statusCode, ok := status.(float64); ok && statusCode >= 400 {
							c.logger.Errorf("文档 %d 操作失败: action=%s, result=%v", i, action, result)
						}
					}
				}
			}
		}
	}

	c.logger.Infof("成功批量处理 %d 个文档到索引 %s", len(documents), indexName)
	return nil
}

// indexExists 检查索引是否存在
func (c *Client) indexExists(indexName string) (bool, error) {
	req := esapi.IndicesExistsRequest{
		Index: []string{indexName},
	}

	res, err := req.Do(context.Background(), c.readClient)
	if err != nil {
		return false, err
	}
	defer res.Body.Close()

	return res.StatusCode == 200, nil
}

// ensureIndex 确保索引存在
func (c *Client) ensureIndex(indexName string) error {
	exists, err := c.indexExists(indexName)
	if err != nil {
		return err
	}

	if exists {
		return nil
	}

	// 创建索引
	mapping := c.getIndexMapping()
	mappingBytes, err := json.Marshal(mapping)
	if err != nil {
		return fmt.Errorf("序列化索引映射失败: %w", err)
	}

	req := esapi.IndicesCreateRequest{
		Index: indexName,
		Body:  bytes.NewReader(mappingBytes),
	}

	res, err := req.Do(context.Background(), c.writeClient)
	if err != nil {
		return fmt.Errorf("创建索引失败: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		// 如果索引已存在，不视为错误
		if res.StatusCode == 400 && strings.Contains(res.String(), "resource_already_exists_exception") {
			c.logger.Debugf("索引 %s 已存在，跳过创建", indexName)
			return nil
		}
		return fmt.Errorf("创建索引请求失败: %s", res.String())
	}

	c.logger.Infof("成功创建索引: %s", indexName)
	return nil
}

// getIndexMapping 获取索引映射
func (c *Client) getIndexMapping() map[string]interface{} {
	return map[string]interface{}{
		"mappings": map[string]interface{}{
			"properties": map[string]interface{}{
				// 基础信息
				"clusterId":     map[string]interface{}{"type": "keyword"},
				"clusterName":   map[string]interface{}{"type": "text", "fields": map[string]interface{}{"keyword": map[string]interface{}{"type": "keyword"}}},
				"clusterType":   map[string]interface{}{"type": "keyword"},
				"clusterStatus": map[string]interface{}{"type": "keyword"},
				"region":        map[string]interface{}{"type": "keyword"},
				"appId":         map[string]interface{}{"type": "keyword"},
				"version":       map[string]interface{}{"type": "keyword"},

				// 账户信息
				"accountName":  map[string]interface{}{"type": "text", "fields": map[string]interface{}{"keyword": map[string]interface{}{"type": "keyword"}}},
				"accountType":  map[string]interface{}{"type": "keyword"},
				"accountLevel": map[string]interface{}{"type": "keyword"},
				"isBigUser":    map[string]interface{}{"type": "integer"},

				// 创建者信息
				"uin":           map[string]interface{}{"type": "keyword"},
				"subAccountUin": map[string]interface{}{"type": "keyword"},

				// 资源信息
				"clusterLevel": map[string]interface{}{"type": "keyword"},
				"nodeCount":    map[string]interface{}{"type": "integer"},
				"cpuCount":     map[string]interface{}{"type": "integer"},
				"memCount":     map[string]interface{}{"type": "integer"},
				"eksPodCount":  map[string]interface{}{"type": "integer"},

				// 网络信息
				"vpcId":         map[string]interface{}{"type": "keyword"},
				"clusterCIDR":   map[string]interface{}{"type": "keyword"},
				"serviceCIDR":   map[string]interface{}{"type": "keyword"},
				"metaClusterId": map[string]interface{}{"type": "keyword"},

				// 组件版本信息
				"deployAddonVersion": map[string]interface{}{"type": "object"},

				// 动态数据
				"tags":   map[string]interface{}{"type": "flattened"},
				"cmdbMd": map[string]interface{}{"type": "flattened"},

				// 时间信息
				"date":      map[string]interface{}{"type": "keyword"},
				"createdAt": map[string]interface{}{"type": "date"},
				"updatedAt": map[string]interface{}{"type": "date"},

				// 原始数据
				"rawData": map[string]interface{}{"type": "object", "enabled": false},
			},
		},
	}
}

// scrollAllClusters 使用scroll API获取所有集群数据
func (c *Client) scrollAllClusters(ctx context.Context, indexName string) (map[string]model.ESClusterDocument, error) {
	result := make(map[string]model.ESClusterDocument)

	// 初始搜索
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"match_all": map[string]interface{}{},
		},
		"size": 1000, // 每批1000条
	}

	queryBytes, err := json.Marshal(query)
	if err != nil {
		return nil, fmt.Errorf("构建查询失败: %w", err)
	}

	req := esapi.SearchRequest{
		Index:  []string{indexName},
		Body:   bytes.NewReader(queryBytes),
		Scroll: time.Minute * 5, // 5分钟scroll超时
	}

	res, err := req.Do(ctx, c.readClient)
	if err != nil {
		return nil, fmt.Errorf("执行搜索失败: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, fmt.Errorf("搜索请求失败: %s", res.String())
	}

	var searchResult struct {
		ScrollID string `json:"_scroll_id"`
		Hits     struct {
			Total struct {
				Value int `json:"value"`
			} `json:"total"`
			Hits []struct {
				ID     string                  `json:"_id"`
				Source model.ESClusterDocument `json:"_source"`
			} `json:"hits"`
		} `json:"hits"`
	}

	if err := json.NewDecoder(res.Body).Decode(&searchResult); err != nil {
		return nil, fmt.Errorf("解析搜索结果失败: %w", err)
	}

	// 处理第一批结果
	for _, hit := range searchResult.Hits.Hits {
		result[hit.Source.ClusterID] = hit.Source
	}

	c.logger.Infof("总计需要获取 %d 个集群，已获取 %d 个", searchResult.Hits.Total.Value, len(result))

	// 继续scroll获取剩余数据
	for len(searchResult.Hits.Hits) > 0 {
		scrollReq := esapi.ScrollRequest{
			ScrollID: searchResult.ScrollID,
			Scroll:   time.Minute * 5,
		}

		scrollRes, err := scrollReq.Do(ctx, c.readClient)
		if err != nil {
			return nil, fmt.Errorf("执行scroll失败: %w", err)
		}
		defer scrollRes.Body.Close()

		if scrollRes.IsError() {
			return nil, fmt.Errorf("scroll请求失败: %s", scrollRes.String())
		}

		if err := json.NewDecoder(scrollRes.Body).Decode(&searchResult); err != nil {
			return nil, fmt.Errorf("解析scroll结果失败: %w", err)
		}

		// 处理当前批次结果
		for _, hit := range searchResult.Hits.Hits {
			result[hit.Source.ClusterID] = hit.Source
		}

		c.logger.Debugf("已获取 %d 个集群", len(result))
	}

	c.logger.Infof("使用scroll API获取到 %d 个集群数据", len(result))
	return result, nil
}

// queryClustersByIds 根据集群ID列表查询集群数据
func (c *Client) queryClustersByIds(ctx context.Context, indexName string, clusterIds []string) (map[string]model.ESClusterDocument, error) {
	// 构建查询，只根据集群ID查询（索引名已包含日期）
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"terms": map[string]interface{}{
				"clusterId": clusterIds,
			},
		},
		"size": len(clusterIds),
	}

	queryBytes, err := json.Marshal(query)
	if err != nil {
		return nil, fmt.Errorf("构建查询失败: %w", err)
	}

	req := esapi.SearchRequest{
		Index: []string{indexName},
		Body:  bytes.NewReader(queryBytes),
	}

	res, err := req.Do(ctx, c.readClient)
	if err != nil {
		return nil, fmt.Errorf("执行搜索失败: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, fmt.Errorf("搜索请求失败: %s", res.String())
	}

	var searchResult struct {
		Hits struct {
			Hits []struct {
				ID     string                  `json:"_id"`
				Source model.ESClusterDocument `json:"_source"`
			} `json:"hits"`
		} `json:"hits"`
	}

	if err := json.NewDecoder(res.Body).Decode(&searchResult); err != nil {
		return nil, fmt.Errorf("解析搜索结果失败: %w", err)
	}

	// 构建结果映射
	result := make(map[string]model.ESClusterDocument)
	for _, hit := range searchResult.Hits.Hits {
		result[hit.Source.ClusterID] = hit.Source
	}

	return result, nil
}

// queryTagsFromWriteES 从写入ES查询集群标签数据
func (c *Client) queryTagsFromWriteES(ctx context.Context, indexName string, clusterIds []string) (map[string]model.ESClusterDocument, error) {
	// 构建查询，只获取必要的字段（tags, cmdbMd, 账户信息等）
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"terms": map[string]interface{}{
				"clusterId": clusterIds,
			},
		},
		"_source": []string{
			"clusterId", "tags", "cmdbMd",
			"accountName", "accountType", "accountLevel", "isBigUser",
			"nodeCount", "cpuCount", "memCount", "eksPodCount",
		},
		"size": len(clusterIds),
	}

	queryBytes, err := json.Marshal(query)
	if err != nil {
		return nil, fmt.Errorf("构建写入ES查询失败: %w", err)
	}

	// 使用写入ES客户端查询
	req := esapi.SearchRequest{
		Index: []string{indexName},
		Body:  bytes.NewReader(queryBytes),
	}

	res, err := req.Do(ctx, c.writeClient)
	if err != nil {
		return nil, fmt.Errorf("执行写入ES查询失败: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		// 如果是索引不存在的错误，返回空结果而不是错误
		if res.StatusCode == 404 {
			c.logger.Infof("写入ES索引 %s 不存在（可能是第一次运行），返回空标签结果", indexName)
			return make(map[string]model.ESClusterDocument), nil
		}
		return nil, fmt.Errorf("写入ES查询请求失败: %s", res.String())
	}

	var searchResult struct {
		Hits struct {
			Hits []struct {
				ID     string                  `json:"_id"`
				Source model.ESClusterDocument `json:"_source"`
			} `json:"hits"`
		} `json:"hits"`
	}

	if err := json.NewDecoder(res.Body).Decode(&searchResult); err != nil {
		return nil, fmt.Errorf("解析写入ES查询结果失败: %w", err)
	}

	// 构建结果映射
	result := make(map[string]model.ESClusterDocument)
	for _, hit := range searchResult.Hits.Hits {
		result[hit.Source.ClusterID] = hit.Source
	}

	c.logger.Debugf("从写入ES查询到 %d 个集群的标签数据", len(result))
	return result, nil
}

// GetExistingClustersFromReadES 从读取ES获取集群业务信息
func (c *Client) GetExistingClustersFromReadES(ctx context.Context, date time.Time, clusterIds []string, dayOffset int, clusterType string) (map[string]model.ESClusterDocument, error) {
	if len(clusterIds) == 0 {
		c.logger.Warnf("读取ES查询参数为空: clusterIds=%d", len(clusterIds))
		return make(map[string]model.ESClusterDocument), nil
	}

	// 计算实际查询的日期
	queryDate := date.AddDate(0, 0, dayOffset)
	queryDateStr := queryDate.Format("2006-01-02")

	// 读取ES使用具体的日期索引
	indexName := c.GetIndexNameByType(queryDate, clusterType)

	c.logger.Infof("从读取ES获取业务信息: 同步日期=%s, 查询日期=%s (偏移%d天), 集群数量=%d, 索引=%s",
		date.Format("2006-01-02"), queryDateStr, dayOffset, len(clusterIds), indexName)

	// 检查索引是否存在
	exists, err := c.indexExistsInReadES(indexName)
	if err != nil {
		c.logger.Warnf("检查读取ES索引存在性失败: %v，继续处理", err)
	}

	if !exists {
		c.logger.Infof("读取ES索引 %s 不存在，返回空结果", indexName)
		return make(map[string]model.ESClusterDocument), nil
	}

	// 分批查询（每批1000个ID）
	result := make(map[string]model.ESClusterDocument)
	batchSize := 1000

	for i := 0; i < len(clusterIds); i += batchSize {
		end := i + batchSize
		if end > len(clusterIds) {
			end = len(clusterIds)
		}

		batchIds := clusterIds[i:end]
		batchResult, err := c.queryBusinessInfoFromReadES(ctx, indexName, batchIds, queryDateStr)
		if err != nil {
			return nil, fmt.Errorf("批量查询读取ES业务信息失败: %w", err)
		}

		// 合并结果
		for id, doc := range batchResult {
			result[id] = doc
		}
	}

	c.logger.Infof("从读取ES获取到 %d 个集群的业务信息", len(result))
	return result, nil
}

// indexExistsInReadES 检查读取ES中的索引是否存在
func (c *Client) indexExistsInReadES(indexName string) (bool, error) {
	req := esapi.IndicesExistsRequest{
		Index: []string{indexName},
	}

	res, err := req.Do(context.Background(), c.readClient)
	if err != nil {
		return false, err
	}
	defer res.Body.Close()

	return res.StatusCode == 200, nil
}

// queryBusinessInfoFromReadES 从读取ES查询集群业务信息
func (c *Client) queryBusinessInfoFromReadES(ctx context.Context, indexName string, clusterIds []string, dateStr string) (map[string]model.ESClusterDocument, error) {
	// 构建查询，包含集群ID和日期过滤，只获取业务相关字段
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": []map[string]interface{}{
					{
						"terms": map[string]interface{}{
							"clusterId": clusterIds,
						},
					},
					{
						"term": map[string]interface{}{
							"date": dateStr,
						},
					},
				},
			},
		},
		"_source": []string{
			"clusterId", "accountName", "accountType", "accountLevel", "isBigUser",
			"nodeCount", "cpuCount", "memCount", "eksPodCount",
		},
		"size": len(clusterIds),
	}

	queryBytes, err := json.Marshal(query)
	if err != nil {
		return nil, fmt.Errorf("构建读取ES查询失败: %w", err)
	}

	c.logger.Debugf("读取ES查询: 索引=%s, 日期=%s, 集群数量=%d", indexName, dateStr, len(clusterIds))
	c.logger.Debugf("读取ES查询语句: %s", string(queryBytes))

	// 使用读取ES客户端查询
	req := esapi.SearchRequest{
		Index: []string{indexName},
		Body:  bytes.NewReader(queryBytes),
	}

	res, err := req.Do(ctx, c.readClient)
	if err != nil {
		return nil, fmt.Errorf("执行读取ES查询失败: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		// 如果是索引不存在的错误，返回空结果而不是错误
		if res.StatusCode == 404 {
			c.logger.Infof("读取ES索引 %s 不存在，返回空业务信息结果", indexName)
			return make(map[string]model.ESClusterDocument), nil
		}
		return nil, fmt.Errorf("读取ES查询请求失败: %s", res.String())
	}

	var searchResult struct {
		Hits struct {
			Hits []struct {
				ID     string                  `json:"_id"`
				Source model.ESClusterDocument `json:"_source"`
			} `json:"hits"`
		} `json:"hits"`
	}

	if err := json.NewDecoder(res.Body).Decode(&searchResult); err != nil {
		return nil, fmt.Errorf("解析读取ES查询结果失败: %w", err)
	}

	// 构建结果映射
	result := make(map[string]model.ESClusterDocument)
	for _, hit := range searchResult.Hits.Hits {
		result[hit.Source.ClusterID] = hit.Source
	}

	c.logger.Debugf("从读取ES查询到 %d 个集群的业务信息", len(result))
	return result, nil
}

// GetReadClient 获取读取ES客户端
func (c *Client) GetReadClient() *elasticsearch.Client {
	return c.readClient
}

// GetWriteClient 获取写入ES客户端
func (c *Client) GetWriteClient() *elasticsearch.Client {
	return c.writeClient
}

// GetClusterByID 根据ID获取集群信息
func (c *Client) GetClusterByID(ctx context.Context, indexName, clusterID string) (*model.ESClusterDocument, error) {
	req := esapi.GetRequest{
		Index:      indexName,
		DocumentID: clusterID,
	}

	res, err := req.Do(ctx, c.writeClient)
	if err != nil {
		return nil, fmt.Errorf("获取集群文档失败: %w", err)
	}
	defer res.Body.Close()

	if res.StatusCode == 404 {
		return nil, nil // 文档不存在
	}

	if res.IsError() {
		return nil, fmt.Errorf("获取集群文档请求失败: %s", res.String())
	}

	var result struct {
		Source model.ESClusterDocument `json:"_source"`
	}

	if err := json.NewDecoder(res.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("解析集群文档失败: %w", err)
	}

	return &result.Source, nil
}

// UpdateClusterTags 更新集群标签
func (c *Client) UpdateClusterTags(ctx context.Context, indexName, clusterID string, tags map[string]interface{}, updatedAt time.Time) error {
	updateDoc := map[string]interface{}{
		"tags":      tags,
		"updatedAt": updatedAt,
	}

	updateBody := map[string]interface{}{
		"doc": updateDoc,
	}

	updateBytes, err := json.Marshal(updateBody)
	if err != nil {
		return fmt.Errorf("构建更新文档失败: %w", err)
	}

	req := esapi.UpdateRequest{
		Index:      indexName,
		DocumentID: clusterID,
		Body:       bytes.NewReader(updateBytes),
	}

	res, err := req.Do(ctx, c.writeClient)
	if err != nil {
		return fmt.Errorf("更新集群标签失败: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("更新集群标签请求失败: %s", res.String())
	}

	return nil
}

// GetAllTagKeys 获取所有标签键
func (c *Client) GetAllTagKeys(ctx context.Context, indexName string) ([]string, error) {
	// 使用聚合查询获取所有标签键
	aggQuery := map[string]interface{}{
		"size": 0,
		"aggs": map[string]interface{}{
			"tag_keys": map[string]interface{}{
				"nested": map[string]interface{}{
					"path": "tags",
				},
				"aggs": map[string]interface{}{
					"keys": map[string]interface{}{
						"terms": map[string]interface{}{
							"field": "tags",
							"size":  1000,
						},
					},
				},
			},
		},
	}

	queryBytes, err := json.Marshal(aggQuery)
	if err != nil {
		return nil, fmt.Errorf("构建聚合查询失败: %w", err)
	}

	req := esapi.SearchRequest{
		Index: []string{indexName},
		Body:  bytes.NewReader(queryBytes),
	}

	res, err := req.Do(ctx, c.writeClient)
	if err != nil {
		return nil, fmt.Errorf("执行聚合查询失败: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, fmt.Errorf("聚合查询请求失败: %s", res.String())
	}

	// 简化实现：返回常用的标签键
	return []string{"environment", "team", "project", "owner", "cost-center"}, nil
}

// GetTagValues 获取指定标签键的所有值
func (c *Client) GetTagValues(ctx context.Context, indexName, tagKey string) ([]interface{}, error) {
	// 简化实现：返回示例值
	return []interface{}{"production", "staging", "development"}, nil
}

// GetTagStatistics 获取标签统计信息
func (c *Client) GetTagStatistics(ctx context.Context, indexName string) (map[string]interface{}, error) {
	// 简化实现：返回统计信息
	return map[string]interface{}{
		"total_clusters":    100,
		"tagged_clusters":   80,
		"untagged_clusters": 20,
		"tag_coverage":      0.8,
	}, nil
}

// Close 关闭连接
func (c *Client) Close() error {
	// Elasticsearch客户端不需要显式关闭
	return nil
}
