package ops

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"git.woa.com/kmetis/cmdb/internal/model"
	"git.woa.com/kmetis/cmdb/pkg/logger"
	"github.com/elastic/go-elasticsearch/v7"
	"github.com/elastic/go-elasticsearch/v7/esapi"
)

// Reader ES读取操作
type Reader struct {
	readClient *elasticsearch.Client
	logger     logger.Logger
}

// NewReader 创建ES读取器
func NewReader(readClient *elasticsearch.Client, logger logger.Logger) *Reader {
	return &Reader{
		readClient: readClient,
		logger:     logger,
	}
}

// GetBusinessInfoFromReadES 从读取ES获取业务信息
func (r *Reader) GetBusinessInfoFromReadES(ctx context.Context, date time.Time, clusterIds []string, clusterType string) (map[string]model.ESClusterDocument, error) {
	if len(clusterIds) == 0 {
		r.logger.Warnf("读取ES查询参数为空: clusterIds=%d", len(clusterIds))
		return make(map[string]model.ESClusterDocument), nil
	}

	// 构建索引名 - 使用具体的日期索引
	indexName := r.buildReadESIndexName(date, clusterType)

	r.logger.Infof("从读取ES获取业务信息: 查询日期=%s, 集群数量=%d, 索引=%s",
		date.Format("2006-01-02"), len(clusterIds), indexName)

	// 检查索引是否存在
	exists, err := r.indexExists(ctx, indexName)
	if err != nil {
		r.logger.Warnf("检查读取ES索引存在性失败: %v，继续处理", err)
	}

	if !exists {
		r.logger.Infof("读取ES索引 %s 不存在，返回空结果", indexName)
		return make(map[string]model.ESClusterDocument), nil
	}

	// 分批查询（每批1000个ID）
	result := make(map[string]model.ESClusterDocument)
	batchSize := 1000

	for i := 0; i < len(clusterIds); i += batchSize {
		end := i + batchSize
		if end > len(clusterIds) {
			end = len(clusterIds)
		}

		batchIds := clusterIds[i:end]
		batchResult, err := r.queryBusinessInfo(ctx, indexName, batchIds)
		if err != nil {
			return nil, fmt.Errorf("批量查询读取ES业务信息失败: %w", err)
		}

		// 合并结果
		for id, doc := range batchResult {
			result[id] = doc
		}
	}

	r.logger.Infof("从读取ES获取到 %d 个集群的业务信息", len(result))
	return result, nil
}

// buildReadESIndexName 构建读取ES索引名
func (r *Reader) buildReadESIndexName(date time.Time, clusterType string) string {
	var prefix string
	switch clusterType {
	case "tke":
		prefix = "tke-cluster"
	case "eks":
		prefix = "eks-cluster"
	default:
		prefix = "tke-cluster"
	}
	return fmt.Sprintf("%s-%s", prefix, date.Format("2006-01-02"))
}

// buildWriteESIndexName 构建写入ES索引名（带starship前缀）
func (r *Reader) buildWriteESIndexName(date time.Time, clusterType string) string {
	var prefix string
	switch clusterType {
	case "tke":
		prefix = "starship-tke-cluster"
	case "eks":
		prefix = "starship-eks-cluster"
	default:
		prefix = "starship-tke-cluster"
	}
	return fmt.Sprintf("%s-%s", prefix, date.Format("2006-01-02"))
}

// indexExists 检查索引是否存在
func (r *Reader) indexExists(ctx context.Context, indexName string) (bool, error) {
	req := esapi.IndicesExistsRequest{
		Index: []string{indexName},
	}

	res, err := req.Do(ctx, r.readClient)
	if err != nil {
		return false, err
	}
	defer res.Body.Close()

	return res.StatusCode == 200, nil
}

// queryBusinessInfo 查询业务信息
func (r *Reader) queryBusinessInfo(ctx context.Context, indexName string, clusterIds []string) (map[string]model.ESClusterDocument, error) {
	// 构建查询，只获取业务相关字段（使用正确的ES字段名）
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"terms": map[string]interface{}{
				"clusterID": clusterIds,
			},
		},
		"_source": []string{
			"clusterID", "accountName", "accountType", "accountLevel", "isBigUser",
			"nodeCount", "cpuCount", "memCount", "eksPodCount",
		},
		"size": len(clusterIds),
	}

	queryBytes, err := json.Marshal(query)
	if err != nil {
		return nil, fmt.Errorf("构建读取ES查询失败: %w", err)
	}

	r.logger.Debugf("读取ES查询: 索引=%s, 集群数量=%d", indexName, len(clusterIds))
	r.logger.Debugf("读取ES查询语句: %s", string(queryBytes))

	// 执行查询
	req := esapi.SearchRequest{
		Index: []string{indexName},
		Body:  bytes.NewReader(queryBytes),
	}

	res, err := req.Do(ctx, r.readClient)
	if err != nil {
		return nil, fmt.Errorf("执行读取ES查询失败: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		// 如果是索引不存在的错误，返回空结果而不是错误
		if res.StatusCode == 404 {
			r.logger.Infof("读取ES索引 %s 不存在，返回空业务信息结果", indexName)
			return make(map[string]model.ESClusterDocument), nil
		}
		return nil, fmt.Errorf("读取ES查询请求失败: %s", res.String())
	}

	var searchResult struct {
		Hits struct {
			Total struct {
				Value int64 `json:"value"`
			} `json:"total"`
			Hits []struct {
				ID     string                  `json:"_id"`
				Source model.ESClusterDocument `json:"_source"`
			} `json:"hits"`
		} `json:"hits"`
	}

	if err := json.NewDecoder(res.Body).Decode(&searchResult); err != nil {
		return nil, fmt.Errorf("解析读取ES查询结果失败: %w", err)
	}

	// 构建结果映射
	result := make(map[string]model.ESClusterDocument)
	for _, hit := range searchResult.Hits.Hits {
		result[hit.Source.ClusterID] = hit.Source
	}

	r.logger.Infof("读取ES查询成功: 总数=%d, 返回=%d", searchResult.Hits.Total.Value, len(result))
	return result, nil
}

// GetTagsFromWriteES 从写入ES获取动态标签
func (r *Reader) GetTagsFromWriteES(ctx context.Context, writeClient *elasticsearch.Client, date time.Time, clusterIds []string, clusterType string) (map[string]model.ESClusterDocument, error) {
	if len(clusterIds) == 0 {
		return make(map[string]model.ESClusterDocument), nil
	}

	// 构建写入ES索引名 - 使用带starship前缀的索引
	indexName := r.buildWriteESIndexName(date, clusterType)

	r.logger.Infof("从写入ES获取动态标签: 查询日期=%s, 集群数量=%d, 索引=%s",
		date.Format("2006-01-02"), len(clusterIds), indexName)

	// 检查索引是否存在
	exists, err := r.indexExistsInWriteES(ctx, writeClient, indexName)
	if err != nil {
		r.logger.Warnf("检查写入ES索引存在性失败: %v，继续处理", err)
	}

	if !exists {
		r.logger.Infof("写入ES索引 %s 不存在，返回空标签结果", indexName)
		return make(map[string]model.ESClusterDocument), nil
	}

	// 分批查询标签
	result := make(map[string]model.ESClusterDocument)
	batchSize := 1000

	for i := 0; i < len(clusterIds); i += batchSize {
		end := i + batchSize
		if end > len(clusterIds) {
			end = len(clusterIds)
		}

		batchIds := clusterIds[i:end]
		batchResult, err := r.queryTags(ctx, writeClient, indexName, batchIds)
		if err != nil {
			return nil, fmt.Errorf("批量查询写入ES标签失败: %w", err)
		}

		// 合并结果
		for id, doc := range batchResult {
			result[id] = doc
		}
	}

	r.logger.Infof("从写入ES获取到 %d 个集群的动态标签", len(result))
	return result, nil
}

// indexExistsInWriteES 检查写入ES中的索引是否存在
func (r *Reader) indexExistsInWriteES(ctx context.Context, writeClient *elasticsearch.Client, indexName string) (bool, error) {
	req := esapi.IndicesExistsRequest{
		Index: []string{indexName},
	}

	res, err := req.Do(ctx, writeClient)
	if err != nil {
		return false, err
	}
	defer res.Body.Close()

	return res.StatusCode == 200, nil
}

// queryTags 查询动态标签
func (r *Reader) queryTags(ctx context.Context, writeClient *elasticsearch.Client, indexName string, clusterIds []string) (map[string]model.ESClusterDocument, error) {
	// 构建查询，只获取标签相关字段（使用正确的ES字段名）
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"terms": map[string]interface{}{
				"clusterID.keyword": clusterIds, // 使用.keyword进行精确匹配
			},
		},
		"_source": []string{
			"clusterID", "tags", "createdAt",
		},
		"size": len(clusterIds),
	}

	queryBytes, err := json.Marshal(query)
	if err != nil {
		return nil, fmt.Errorf("构建写入ES标签查询失败: %w", err)
	}

	r.logger.Infof("写入ES标签查询: 索引=%s, 集群数量=%d, 查询体=%s", indexName, len(clusterIds), string(queryBytes))

	// 执行查询
	req := esapi.SearchRequest{
		Index: []string{indexName},
		Body:  bytes.NewReader(queryBytes),
	}

	res, err := req.Do(ctx, writeClient)
	if err != nil {
		return nil, fmt.Errorf("执行写入ES标签查询失败: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		if res.StatusCode == 404 {
			r.logger.Infof("写入ES索引 %s 不存在，返回空标签结果", indexName)
			return make(map[string]model.ESClusterDocument), nil
		}
		return nil, fmt.Errorf("写入ES标签查询请求失败: %s", res.String())
	}

	var searchResult struct {
		Hits struct {
			Hits []struct {
				ID     string                  `json:"_id"`
				Source model.ESClusterDocument `json:"_source"`
			} `json:"hits"`
		} `json:"hits"`
	}

	if err := json.NewDecoder(res.Body).Decode(&searchResult); err != nil {
		return nil, fmt.Errorf("解析写入ES标签查询结果失败: %w", err)
	}

	// 构建结果映射
	result := make(map[string]model.ESClusterDocument)
	for _, hit := range searchResult.Hits.Hits {
		result[hit.Source.ClusterID] = hit.Source
	}

	r.logger.Debugf("从写入ES查询到 %d 个集群的标签数据", len(result))
	return result, nil
}
