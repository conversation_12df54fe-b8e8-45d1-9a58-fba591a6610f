# CMDB并发同步优化指南

## 🚀 概述

本文档介绍CMDB元数据中心的并发同步优化方案，通过引入多Worker并发处理，将原本2小时的全量同步时间优化到30分钟以内。

## 📊 性能对比

| 模式 | Worker数 | 预期耗时 | 性能提升 | 适用场景 |
|------|----------|----------|----------|----------|
| 串行模式 | 1 | 120分钟 | 基准 | 小规模数据 |
| 并发模式 | 4 | 40分钟 | 3x | 中等规模 |
| 并发模式 | 8 | 20分钟 | 6x | 大规模数据 |
| 并发模式 | 12 | 15分钟 | 8x | 超大规模 |

## ⚙️ 配置说明

### 并发配置参数

```yaml
# 同步配置
sync:
  batch_size: 2000              # 批次大小，建议1000-3000
  concurrent_workers: 8         # 并发Worker数量，建议4-16
  component_workers: 4          # 组件收集并发数，建议2-8
  retry_attempts: 3             # 失败重试次数
  retry_delay: "5s"             # 重试延迟
```

### ClickHouse连接池优化

```yaml
clickhouse:
  max_open_conns: 20           # 最大连接数，建议≥并发Worker数
  max_idle_conns: 10           # 空闲连接数，建议为max_open_conns的一半
```

### Elasticsearch批量配置

```yaml
elasticsearch:
  bulk_size: 1000              # 批量写入大小
  bulk_workers: 4              # 批量写入Worker数
```

## 🔧 使用方法

### 1. 启用并发模式

设置 `concurrent_workers > 1` 即可自动启用并发模式：

```yaml
sync:
  concurrent_workers: 8        # 设置为大于1的值
```

### 2. 运行同步

```bash
# 使用并发模式同步
go run cmd/main.go -config=config/config.yaml -date=2025-06-29

# 试运行模式（不实际写入数据）
go run cmd/main.go -config=config/config.yaml -date=2025-06-29 -dry-run
```

### 3. 性能测试

```bash
# 运行性能对比测试
./scripts/performance_test.sh
```

## 📈 性能监控

### 实时监控指标

并发同步过程中会显示以下监控信息：

```
🚀 开始并发同步集群数据，日期: 2025-06-29, 并发Worker数: 8
📊 集群分类: TKE=35000, EKS=5000
⚡ 预处理完成，耗时: 2m30s
📈 进度报告: 45.0% (9/20批次), 已处理18000集群, 耗时15m, 预计剩余18m
🎉 并发同步完成! 总耗时: 25m (预处理: 2m30s, 并发处理: 22m30s)
```

### 性能指标说明

- **总耗时**: 完整同步流程的总时间
- **预处理时间**: 批量获取基础数据的时间
- **并发处理时间**: 实际并发处理集群的时间
- **集群/秒**: 处理速度指标
- **批次统计**: 成功/失败批次数量

## 🎯 优化建议

### 1. Worker数量调优

| 集群规模 | 建议Worker数 | 说明 |
|----------|--------------|------|
| < 10,000 | 4-6 | 避免过度并发 |
| 10,000-30,000 | 6-10 | 平衡性能和资源 |
| 30,000-50,000 | 8-12 | 充分利用并发 |
| > 50,000 | 10-16 | 最大化性能 |

### 2. 批次大小调优

- **小批次** (500-1000): 更快的错误恢复，但增加调度开销
- **中批次** (1000-2000): 平衡性能和内存使用
- **大批次** (2000-3000): 最大化吞吐量，但增加内存压力

### 3. 数据库连接优化

```yaml
clickhouse:
  max_open_conns: 20           # 建议为Worker数的1.5-2倍
  max_idle_conns: 10           # 建议为max_open_conns的一半
  read_timeout: "60s"          # 增加读取超时
  write_timeout: "60s"         # 增加写入超时
```

## 🔍 故障排查

### 常见问题

1. **连接池耗尽**
   ```
   错误: 获取ClickHouse连接失败
   解决: 增加max_open_conns或减少concurrent_workers
   ```

2. **内存不足**
   ```
   错误: 预处理数据失败，内存不足
   解决: 减少batch_size或增加系统内存
   ```

3. **ES写入超时**
   ```
   错误: 批量写入ES失败
   解决: 增加ES超时时间或减少bulk_size
   ```

### 性能调试

启用详细日志：

```yaml
logging:
  level: "debug"               # 启用调试日志
```

查看详细的批次处理信息和组件收集过程。

## 📋 最佳实践

### 1. 生产环境配置

```yaml
sync:
  batch_size: 2000
  concurrent_workers: 8
  component_workers: 4
  retry_attempts: 3
  retry_delay: "10s"

clickhouse:
  max_open_conns: 16
  max_idle_conns: 8
  read_timeout: "60s"

elasticsearch:
  bulk_size: 1000
  bulk_workers: 4
  timeout: "60s"
```

### 2. 监控和告警

- 监控同步耗时，设置告警阈值（如超过45分钟）
- 监控错误率，设置告警阈值（如错误率>5%）
- 监控数据库连接数，避免连接池耗尽

### 3. 容量规划

- **CPU**: 并发模式会增加CPU使用，建议预留50%余量
- **内存**: 预处理阶段会缓存大量数据，建议8GB+内存
- **网络**: 并发查询会增加网络流量，确保带宽充足

## 🔄 回滚方案

如果并发模式出现问题，可以快速回滚到串行模式：

```yaml
sync:
  concurrent_workers: 1        # 设置为1禁用并发模式
```

或者使用环境变量临时禁用：

```bash
export CMDB_CONCURRENT_WORKERS=1
go run cmd/main.go -config=config/config.yaml -date=2025-06-29
```

## 📞 技术支持

如遇到问题，请提供以下信息：

1. 配置文件内容
2. 错误日志
3. 系统资源使用情况
4. 集群规模和数据量

联系方式：[技术支持邮箱]
