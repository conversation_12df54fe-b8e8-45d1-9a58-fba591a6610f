package logger

import (
	"fmt"
	"log"
	"os"
)

// TestLogger 测试用的简单logger实现
type TestLogger struct {
	*log.Logger
}

// NewTestLogger 创建测试logger
func NewTestLogger() Logger {
	return &TestLogger{
		Logger: log.New(os.Stdout, "[TEST] ", log.LstdFlags),
	}
}

// Debug 调试日志
func (l *TestLogger) Debug(args ...interface{}) {
	l.Logger.Print("[DEBUG] ", fmt.Sprint(args...))
}

// Debugf 格式化调试日志
func (l *TestLogger) Debugf(format string, args ...interface{}) {
	l.Logger.Printf("[DEBUG] "+format, args...)
}

// Info 信息日志
func (l *TestLogger) Info(args ...interface{}) {
	l.Logger.Print("[INFO] ", fmt.Sprint(args...))
}

// Infof 格式化信息日志
func (l *TestLogger) Infof(format string, args ...interface{}) {
	l.Logger.Printf("[INFO] "+format, args...)
}

// Warn 警告日志
func (l *TestLogger) Warn(args ...interface{}) {
	l.Logger.Print("[WARN] ", fmt.Sprint(args...))
}

// Warnf 格式化警告日志
func (l *TestLogger) Warnf(format string, args ...interface{}) {
	l.Logger.Printf("[WARN] "+format, args...)
}

// Error 错误日志
func (l *TestLogger) Error(args ...interface{}) {
	l.Logger.Print("[ERROR] ", fmt.Sprint(args...))
}

// Errorf 格式化错误日志
func (l *TestLogger) Errorf(format string, args ...interface{}) {
	l.Logger.Printf("[ERROR] "+format, args...)
}

// Fatal 致命错误日志
func (l *TestLogger) Fatal(args ...interface{}) {
	l.Logger.Fatal("[FATAL] ", fmt.Sprint(args...))
}

// Fatalf 格式化致命错误日志
func (l *TestLogger) Fatalf(format string, args ...interface{}) {
	l.Logger.Fatalf("[FATAL] "+format, args...)
}

// WithField 添加字段（测试实现中忽略）
func (l *TestLogger) WithField(key string, value interface{}) Logger {
	return l
}

// WithFields 添加多个字段（测试实现中忽略）
func (l *TestLogger) WithFields(fields map[string]interface{}) Logger {
	return l
}
