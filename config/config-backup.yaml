# CMDB元数据中心配置文件

# ClickHouse配置
clickhouse:
  host: "*************"
  port: 9000
  database: "tkedata"
  username: "TKE_SRE"
  password: "TKE_CloudNativeSRE666"
  max_open_conns: 10
  max_idle_conns: 5
  conn_max_lifetime: "300s"
  dial_timeout: "10s"
  read_timeout: "30s"
  write_timeout: "30s"
  compress: true
  debug: false

# MySQL配置
mysql:
  host: "************"
  port: 3306
  database: "starship_cmdb"
  username: "starship_cmdb"
  password: "_R8!du--bvCH7WqAv3aQ"
  max_open_conns: 10
  max_idle_conns: 5
  conn_max_lifetime: "300s"

# Elasticsearch配置
elasticsearch:
  # 读取配置
  read_config:
    host: "************"
    port: 9200
    username: "elastic"
    password: "G08bMcIwjL"
    scheme: "http"
    max_retries: 3
    timeout: "30s"
  # 写入配置
  write_config:
    host: "lb-8c0obspy-9t8hen40zgwxklpv.clb.gz-tencentclb.cloud"
    port: 9200
    username: "elastic"
    password: "Q37F3WO6rEreT4Tr"
    scheme: "https"
    max_retries: 3
    timeout: "30s"
  bulk_size: 1000
  bulk_workers: 4

# 同步配置
sync:
  batch_size: 1000
  es_query_offset: -2       # 读取ES查询时间偏移（天），-2表示查询前天的数据
  ch_query_offset: -1       # ClickHouse查询时间偏移（天 ），-1表示查询昨天的数据

# 日志配置
logging:
  level: "info"
  format: "json"

# K8s组件配置
components:
  # === 控制面组件 ===
  kube-apiserver:
    container_names: ["apiserver", "kube-apiserver"]
    component_type: "control-plane"
    platforms:
      tke:
        managed:
          workload_name: "{clusterID}-apiserver"
          query_source: "meta"
        independent:
          workload_name: "kube-apiserver"
          query_source: "user"
      eks:
        workload_name: "{clusterID}-apiserver"
        query_source: "meta"
    description: "Kubernetes API Server"

  kube-scheduler:
    container_names: ["scheduler", "kube-scheduler"]
    component_type: "control-plane"
    platforms:
      tke:
        managed:
          workload_name: "{clusterID}-scheduler"
          query_source: "meta"
        independent:
          workload_name: "kube-scheduler"
          query_source: "user"
      eks:
        workload_name: "{clusterID}-scheduler"
        query_source: "meta"
    description: "Kubernetes Scheduler"

  kube-controller-manager:
    container_names: ["controller-manager", "kube-controller-manager"]
    component_type: "control-plane"
    platforms:
      tke:
        managed:
          workload_name: "{clusterID}-controller-manager"
          query_source: "meta"
        independent:
          workload_name: "kube-controller-manager"
          query_source: "user"
      eks:
        workload_name: "{clusterID}-controller-manager"
        query_source: "meta"
    description: "Kubernetes Controller Manager"

  # === 用户组件 ===
  coredns:
    container_names: ["coredns"]
    component_type: "user"
    platforms:
      tke:
        workload_name: "coredns"
        query_source: "user"
      eks:
        workload_name: "coredns"
        query_source: "user"
    description: "CoreDNS - 用户组件，永远在用户集群"

  kube-proxy:
    container_names: ["kube-proxy"]
    component_type: "user"
    platforms:
      tke:
        workload_name: "kube-proxy"
        query_source: "user"
      eks:
        workload_name: "kube-proxy"
        query_source: "user"
    description: "Kubernetes Proxy - 用户组件，永远在用户集群"

  kubernetes-proxy:
    container_names: [ "kubernetes-proxy" ]
    component_type: "user"
    platforms:
      tke:
        workload_name: "kubernetes-proxy"
        query_source: "user"
      eks:
        workload_name: "kubernetes-proxy"
        query_source: "user"
    description: "Kubernetes Proxy - 用户组件，永远在用户集群"

  # === 特殊组件 ===
  eklet:
    container_names: ["eklet"]
    component_type: "special"
    platforms:
      tke:
        workload_name: "eklet"
        query_source: "meta"
      eks:
        workload_name: "{clusterID}-eklet"
        query_source: "meta"
    description: "Elastic Kubernetes Let - 特殊组件，无论托管/独立都在Meta集群"

# 全局组件配置
global_components:
  default_query_source: "user"
  enable_component_collection: true
  component_collection_timeout: 30